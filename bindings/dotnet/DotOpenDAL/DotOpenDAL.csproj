<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <Title>DotOpenDAL</Title>
        <Description>The official .NET binding for Apache OpenDAL™</Description>
        <Version>0.1.0</Version>
        <AssemblyVersion>$(Version)</AssemblyVersion>
        <FileVersion>$(Version)</FileVersion>
        <Authors>Apache OpenDAL™</Authors>
        <Company>Apache Software Foundation</Company>
        <Copyright>$(Company)</Copyright>
        <PackageTags>Apache;OpenDAL</PackageTags>
        <PackageLicenseExpression>Apache-2.0</PackageLicenseExpression>

        <TargetFramework>net7.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <None Include="../target/debug/libopendal_dotnet.*">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
    </ItemGroup>

</Project>
