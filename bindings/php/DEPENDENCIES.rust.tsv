crate	0BSD	Apache-2.0	Apache-2.0 WITH LLVM-exception	BSD-2-Clause	BSD-3-Clause	BSL-1.0	CC0-1.0	ISC	MIT	MPL-2.0	OpenSSL	Unicode-DFS-2016	Unlicense	Zlib
addr2line@0.24.2		X							X					
adler2@2.0.0	X	X							X					
aes@0.8.4		X							X					
android-tzdata@0.1.1		X							X					
android_system_properties@0.1.5		X							X					
anyhow@1.0.92		X							X					
async-trait@0.1.83		X							X					
autocfg@1.4.0		X							X					
backon@1.2.0		X												
backtrace@0.3.74		X							X					
base64@0.21.7		X							X					
base64@0.22.1		X							X					
base64ct@1.6.0		X							X					
bindgen@0.68.1					X									
bitflags@2.6.0		X							X					
block-buffer@0.10.4		X							X					
block-padding@0.3.3		X							X					
bumpalo@3.16.0		X							X					
bytecount@0.6.8		X							X					
byteorder@1.5.0									X				X	
bytes@1.8.0									X					
bzip2@0.4.4		X							X					
bzip2-sys@0.1.11+1.0.8		X							X					
camino@1.1.9		X							X					
cargo-platform@0.1.8		X							X					
cargo_metadata@0.14.2									X					
cbc@0.1.2		X							X					
cc@1.1.34		X							X					
cexpr@0.6.0		X							X					
cfg-if@1.0.0		X							X					
chrono@0.4.38		X							X					
cipher@0.4.4		X							X					
clang-sys@1.8.1		X												
const-oid@0.9.6		X							X					
const-random@0.1.18		X							X					
const-random-macro@0.1.16		X							X					
constant_time_eq@0.1.5							X							
core-foundation@0.9.4		X							X					
core-foundation-sys@0.8.7		X							X					
cpufeatures@0.2.14		X							X					
crc32c@0.6.8		X							X					
crc32fast@1.4.2		X							X					
crossbeam-utils@0.8.20		X							X					
crunchy@0.2.2									X					
crypto-common@0.1.6		X							X					
darling@0.14.4									X					
darling_core@0.14.4									X					
darling_macro@0.14.4									X					
der@0.7.9		X							X					
deranged@0.3.11		X							X					
digest@0.10.7		X							X					
dlv-list@0.5.2		X							X					
either@1.13.0		X							X					
errno@0.3.9		X							X					
error-chain@0.12.4		X							X					
ext-php-rs@0.11.2		X							X					
ext-php-rs-derive@0.10.1		X							X					
fastrand@2.1.1		X							X					
flagset@0.4.6		X												
flate2@1.0.34		X							X					
fnv@1.0.7		X							X					
foreign-types@0.3.2		X							X					
foreign-types-shared@0.1.1		X							X					
form_urlencoded@1.2.1		X							X					
futures@0.3.31		X							X					
futures-channel@0.3.31		X							X					
futures-core@0.3.31		X							X					
futures-io@0.3.31		X							X					
futures-macro@0.3.31		X							X					
futures-sink@0.3.31		X							X					
futures-task@0.3.31		X							X					
futures-util@0.3.31		X							X					
generic-array@0.14.7									X					
getrandom@0.2.15		X							X					
gimli@0.31.1		X							X					
glob@0.3.1		X							X					
gloo-timers@0.3.0		X							X					
hashbrown@0.14.5		X							X					
hermit-abi@0.3.9		X							X					
hex@0.4.3		X							X					
hmac@0.12.1		X							X					
home@0.5.9		X							X					
http@1.1.0		X							X					
http-body@1.0.1									X					
http-body-util@0.1.2									X					
httparse@1.9.5		X							X					
hyper@1.5.0									X					
hyper-rustls@0.27.3		X						X	X					
hyper-util@0.1.10									X					
iana-time-zone@0.1.61		X							X					
iana-time-zone-haiku@0.1.2		X							X					
ident_case@1.0.1		X							X					
idna@0.5.0		X							X					
inout@0.1.3		X							X					
ipnet@2.10.1		X							X					
itoa@1.0.11		X							X					
jobserver@0.1.32		X							X					
js-sys@0.3.72		X							X					
jsonwebtoken@9.3.0									X					
lazy_static@1.5.0		X							X					
lazycell@1.3.0		X							X					
libc@0.2.161		X							X					
libloading@0.8.5								X						
libm@0.2.11		X							X					
linux-raw-sys@0.4.14		X	X						X					
lock_api@0.4.12		X							X					
log@0.4.22		X							X					
md-5@0.10.6		X							X					
memchr@2.7.4									X				X	
mime@0.3.17		X							X					
minimal-lexical@0.2.1		X							X					
miniz_oxide@0.8.0		X							X					X
mio@1.0.2									X					
native-tls@0.2.12		X							X					
nom@7.1.3									X					
num-bigint@0.4.6		X							X					
num-bigint-dig@0.8.4		X							X					
num-conv@0.1.0		X							X					
num-integer@0.1.46		X							X					
num-iter@0.1.45		X							X					
num-traits@0.2.19		X							X					
object@0.36.5		X							X					
once_cell@1.20.2		X							X					
opendal@0.50.2		X												
opendal-php@0.1.11		X												
openssl@0.10.68		X												
openssl-macros@0.1.1		X							X					
openssl-probe@0.1.5		X							X					
openssl-sys@0.9.104									X					
ordered-multimap@0.7.3									X					
parking_lot@0.12.3		X							X					
parking_lot_core@0.9.10		X							X					
password-hash@0.4.2		X							X					
pbkdf2@0.11.0		X							X					
pbkdf2@0.12.2		X							X					
peeking_take_while@0.1.2		X							X					
pem@3.0.4									X					
pem-rfc7468@0.7.0		X							X					
percent-encoding@2.3.1		X							X					
pin-project-lite@0.2.15		X							X					
pin-utils@0.1.0		X							X					
pkcs1@0.7.5		X							X					
pkcs5@0.7.1		X							X					
pkcs8@0.10.2		X							X					
pkg-config@0.3.31		X							X					
powerfmt@0.2.0		X							X					
ppv-lite86@0.2.20		X							X					
prettyplease@0.2.25		X							X					
proc-macro2@1.0.89		X							X					
pulldown-cmark@0.9.6									X					
quick-xml@0.35.0									X					
quick-xml@0.36.2									X					
quote@1.0.37		X							X					
rand@0.8.5		X							X					
rand_chacha@0.3.1		X							X					
rand_core@0.6.4		X							X					
redox_syscall@0.5.7									X					
regex@1.11.1		X							X					
regex-automata@0.4.8		X							X					
regex-syntax@0.8.5		X							X					
reqsign@0.16.1		X												
reqwest@0.12.9		X							X					
ring@0.17.8											X			
rsa@0.9.6		X							X					
rust-ini@0.21.1									X					
rustc-demangle@0.1.24		X							X					
rustc-hash@1.1.0		X							X					
rustc_version@0.4.1		X							X					
rustix@0.38.38		X	X						X					
rustls@0.23.16		X						X	X					
rustls-pemfile@2.2.0		X						X	X					
rustls-pki-types@1.10.0		X							X					
rustls-webpki@0.102.8								X						
ryu@1.0.18		X				X								
salsa20@0.10.2		X							X					
same-file@1.0.6									X				X	
schannel@0.1.26									X					
scopeguard@1.2.0		X							X					
scrypt@0.11.0		X							X					
security-framework@2.11.1		X							X					
security-framework-sys@2.12.0		X							X					
semver@1.0.23		X							X					
serde@1.0.214		X							X					
serde_derive@1.0.214		X							X					
serde_json@1.0.132		X							X					
serde_urlencoded@0.7.1		X							X					
sha1@0.10.6		X							X					
sha2@0.10.8		X							X					
shlex@1.3.0		X							X					
signature@2.2.0		X							X					
simple_asn1@0.6.2								X						
skeptic@0.13.7		X							X					
slab@0.4.9									X					
smallvec@1.13.2		X							X					
socket2@0.5.7		X							X					
spin@0.9.8									X					
spki@0.7.3		X							X					
strsim@0.10.0									X					
subtle@2.6.1					X									
syn@1.0.109		X							X					
syn@2.0.87		X							X					
sync_wrapper@1.0.1		X												
tempfile@3.13.0		X							X					
thiserror@1.0.67		X							X					
thiserror-impl@1.0.67		X							X					
time@0.3.36		X							X					
time-core@0.1.2		X							X					
time-macros@0.2.18		X							X					
tiny-keccak@2.0.2							X							
tinyvec@1.8.0		X							X					X
tinyvec_macros@0.1.1		X							X					X
tokio@1.41.0									X					
tokio-rustls@0.26.0		X							X					
tokio-util@0.7.12									X					
tower-service@0.3.3									X					
tracing@0.1.40									X					
tracing-core@0.1.32									X					
trim-in-place@0.1.7									X					
try-lock@0.2.5									X					
typenum@1.17.0		X							X					
unicase@2.8.0		X							X					
unicode-bidi@0.3.17		X							X					
unicode-ident@1.0.13		X							X			X		
unicode-normalization@0.1.24		X							X					
untrusted@0.9.0								X						
ureq@2.10.1		X							X					
url@2.5.2		X							X					
uuid@1.11.0		X							X					
vcpkg@0.2.15		X							X					
version_check@0.9.5		X							X					
walkdir@2.5.0									X				X	
want@0.3.1									X					
wasi@0.11.0+wasi-snapshot-preview1		X	X						X					
wasm-bindgen@0.2.95		X							X					
wasm-bindgen-backend@0.2.95		X							X					
wasm-bindgen-futures@0.4.45		X							X					
wasm-bindgen-macro@0.2.95		X							X					
wasm-bindgen-macro-support@0.2.95		X							X					
wasm-bindgen-shared@0.2.95		X							X					
wasm-streams@0.4.2		X							X					
web-sys@0.3.72		X							X					
webpki-roots@0.26.6										X				
which@4.4.2									X					
winapi-util@0.1.9									X				X	
windows-core@0.52.0		X							X					
windows-registry@0.2.0		X							X					
windows-result@0.2.0		X							X					
windows-strings@0.1.0		X							X					
windows-sys@0.52.0		X							X					
windows-sys@0.59.0		X							X					
windows-targets@0.52.6		X							X					
windows_aarch64_gnullvm@0.52.6		X							X					
windows_aarch64_msvc@0.52.6		X							X					
windows_i686_gnu@0.52.6		X							X					
windows_i686_gnullvm@0.52.6		X							X					
windows_i686_msvc@0.52.6		X							X					
windows_x86_64_gnu@0.52.6		X							X					
windows_x86_64_gnullvm@0.52.6		X							X					
windows_x86_64_msvc@0.52.6		X							X					
zerocopy@0.7.35		X		X					X					
zerocopy-derive@0.7.35		X		X					X					
zeroize@1.8.1		X							X					
zip@0.6.6									X					
zstd@0.11.2+zstd.1.5.2									X					
zstd-safe@5.0.2+zstd.1.5.2		X							X					
zstd-sys@2.0.13+zstd.1.5.6		X							X					
