cabal-version:      3.0
-- Licensed to the Apache Software Foundation (ASF) under one
-- or more contributor license agreements.  See the NOTICE file
-- distributed with this work for additional information
-- regarding copyright ownership.  The ASF licenses this file
-- to you under the Apache License, Version 2.0 (the
-- "License"); you may not use this file except in compliance
-- with the License.  You may obtain a copy of the License at
--
--   http://www.apache.org/licenses/LICENSE-2.0
--
-- Unless required by applicable law or agreed to in writing,
-- software distributed under the License is distributed on an
-- "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
-- KIND, either express or implied.  See the License for the
-- specific language governing permissions and limitations
-- under the License.

name:               opendal
version:            *********
license:            Apache-2.0
synopsis:           Apache OpenDAL™ Haskell Binding
description:
    Apache OpenDAL™ Haskell Binding. Open Data Access Layer: Access data freely, painlessly, and efficiently
author:             OpenDAL Contributors
maintainer:         <EMAIL>
category:           Storage, Binding
tested-with:        GHC ==9.4.8
extra-doc-files:    README.md, CONTRIBUTING.md
extra-source-files: src/*.rs, Cargo.toml

build-type:         Custom
custom-setup
    setup-depends:  Cabal, base, directory, process

source-repository head
    type:     git
    location: https://github.com/apache/opendal
    subdir:   bindings/haskell

source-repository this
    type:     git
    location: https://github.com/apache/opendal
    tag:      v0.47.1
    subdir:   bindings/haskell

common base
    ghc-options:      -Wall
    build-depends:
        base >= 4.10 && < 5,
        bytestring >= 0.11.4 && < 0.12,
        mtl >= 2.2.2 && < 2.3,
        text (>= 2.0.2 && < 2.1) || (>= 1.2.5 && < 1.3),
        time >= 1.11.1 && < 1.12,
        unordered-containers >= 0.2.19 && < 0.3,
        co-log >= 0.5.0 && < 0.7,
    default-language: Haskell2010
    default-extensions:
        OverloadedStrings,
        ForeignFunctionInterface,
        DerivingStrategies,
        GeneralizedNewtypeDeriving,
        LambdaCase

library
    import:           base
    exposed-modules:
        OpenDAL
    other-modules:
        OpenDAL.FFI
    hs-source-dirs:   haskell-src
    extra-bundled-libraries: opendal_hs

test-suite opendal-test
    import:           base
    type:             exitcode-stdio-1.0
    main-is:          Spec.hs
    other-modules:    BasicTest
    hs-source-dirs:   test
    build-depends:
        opendal,
        tasty >= 1.5 && < 1.6,
        tasty-hunit >= 0.10.1 && < 0.11,
