# Dependencies

## Rust Part Dependencies

Refer to [DEPENDENCIES.rust.tsv](DEPENDENCIES.rust.tsv) for the full list.

## Haskell Part Dependencies

Check packages listed in `opendal.cabal` from <https://hackage.haskell.org>:

- base: [BSD-3-Clause](https://hackage.haskell.org/package/base-4.19.0.0/src/LICENSE)
- bytestring: [BSD-3-Clause](https://hackage.haskell.org/package/bytestring-0.12.0.2/src/LICENSE)
- mtl: [BSD-3-Clause](https://hackage.haskell.org/package/mtl-2.3.1/src/LICENSE)
- text: [BSD-2-Clause](https://hackage.haskell.org/package/text-2.1/src/LICENSE)
- time: [BSD-2-Clause](https://hackage.haskell.org/package/time-1.12.2/src/LICENSE)
- unordered-containers: [BSD-3-Clause](https://hackage.haskell.org/package/unordered-containers-0.2.19.1/src/LICENSE)
- co-log: [MPL-2.0](https://hackage.haskell.org/package/co-log-0.6.0.2/src/LICENSE)
