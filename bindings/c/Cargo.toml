# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

[package]
name = "opendal-c"
publish = false

authors = ["Apache OpenDAL <<EMAIL>>"]
edition = "2021"
homepage = "https://opendal.apache.org/"
license = "Apache-2.0"
repository = "https://github.com/apache/opendal"
rust-version = "1.75"
version = "0.45.1"

[lib]
crate-type = ["cdylib", "staticlib"]
doc = false

[build-dependencies]
cbindgen = "0.26.0"

[dependencies]
bytes = "1.4.0"
once_cell = "1.17.1"
# this crate won't be published, we always use the local version
opendal = { version = ">=0", path = "../../core", features = [
  "layers-blocking",
  # These are default features before v0.46. TODO: change to optional features #4313
  "services-azblob",
  "services-azdls",
  "services-cos",
  "services-fs",
  "services-gcs",
  "services-ghac",
  "services-http",
  "services-ipmfs",
  "services-memory",
  "services-obs",
  "services-oss",
  "services-s3",
  "services-webdav",
  "services-webhdfs",
  "services-azfile",
] }
tokio = { version = "1.27", features = ["fs", "macros", "rt-multi-thread"] }
