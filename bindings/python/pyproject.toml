# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

[build-system]
build-backend = "maturin"
requires = ["maturin>=1.0,<2.0"]

[project]
classifiers = [
  "Programming Language :: Rust",
  "Programming Language :: Python :: Implementation :: CPython",
]
description = "Apache OpenDAL™ Python Binding"
license = { text = "Apache-2.0" }
name = "opendal"
readme = "README.md"
requires-python = ">=3.10"

dynamic = ['version']

[project.optional-dependencies]
benchmark = [
  "gevent",
  "greenify",
  "greenlet",
  "boto3",
  "pydantic",
  "boto3-stubs[essential]",
]
docs = ["pdoc"]
lint = ["ruff"]
test = ["pytest", "python-dotenv", "pytest-asyncio"]

[project.urls]
Documentation = "https://opendal.apache.org/docs/python/opendal.html"
Homepage = "https://opendal.apache.org/"
Repository = "https://github.com/apache/opendal"

[tool.maturin]
features = ["pyo3/extension-module"]
module-name = "opendal._opendal"
python-source = "python"
strip = true

[tool.ruff.lint]
ignore = ["F403", "F405"]
