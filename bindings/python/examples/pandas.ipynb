{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install the opendal and pandas\n", "!pip install opendal, pandas"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import opendal\n", "import pandas as pd\n", "\n", "# Init an operator.\n", "op = opendal.Operator(\"fs\", root=\"/tmp\")\n", "\n", "# Create and write a csv file\n", "op.write(\"test.csv\", b\"name,age\\n<PERSON><PERSON>,25\\nBob,30\\n<PERSON><PERSON><PERSON>,35\")\n", "\n", "# Open and read the DataFrame from the file.\n", "with op.open(\"test123.csv\", mode=\"rb\") as file:\n", "    read_df = pd.read_csv(file)\n", "    print(f\"read_df: {read_df}\")"]}], "metadata": {"language_info": {"name": "python"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}