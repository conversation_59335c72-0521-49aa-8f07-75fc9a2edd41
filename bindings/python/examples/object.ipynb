{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install the opendal\n", "!pip install opendal"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import opendal\n", "\n", "# Init an operator.\n", "op = opendal.Operator(\"fs\", root=\"/tmp\")\n", "print(f\"operator: {op}\")\n", "\n", "# Write data into object test.\n", "op.write(\"test\", b\"Hello, World!\")\n", "\n", "# Read data from object\n", "bs = op.read(\"test\")\n", "print(f\"content: {bs.decode()}\")\n", "\n", "# Get object metadata.\n", "meta = op.stat(\"test\")\n", "print(f\"metadata: {meta}\")"]}], "metadata": {"language_info": {"name": "python"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}