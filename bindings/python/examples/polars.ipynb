{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install the opendal and polars\n", "!pip install opendal, polars"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import opendal\n", "import polars as pl\n", "\n", "# Init an operator.\n", "op = opendal.Operator(\"fs\", root=\"/tmp\")\n", "\n", "# Create a DataFrame.\n", "df = pl.<PERSON>Frame({\"name\": [\"<PERSON>\", \"<PERSON>\"], \"age\": [20, 30]})\n", "print(f\"df: {df}\")\n", "\n", "# Open and write the DataFrame to the file.\n", "with op.open(\"test.csv\", mode=\"wb\") as file:\n", "    df.write_csv(file)\n", "\n", "# Open and read the DataFrame from the file.\n", "with op.open(\"test.csv\", mode=\"rb\") as file:\n", "    read_df = pl.read_csv(file)\n", "    print(f\"read_df: {read_df}\")"]}], "metadata": {"language_info": {"name": "python"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}