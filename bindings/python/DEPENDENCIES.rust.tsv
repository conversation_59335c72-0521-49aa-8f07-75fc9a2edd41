crate	0BSD	Apache-2.0	Apache-2.0 WITH LLVM-exception	BSD-2-Clause	BSD-3-Clause	BSL-1.0	CC0-1.0	ISC	MIT	MPL-2.0	OpenSSL	Unicode-DFS-2016	Unlicense	Zlib
addr2line@0.24.2		X							X					
adler2@2.0.0	X	X							X					
aes@0.8.4		X							X					
android-tzdata@0.1.1		X							X					
android_system_properties@0.1.5		X							X					
anyhow@1.0.91		X							X					
arc-swap@1.7.1		X							X					
async-trait@0.1.83		X							X					
autocfg@1.4.0		X							X					
awaitable@0.4.0									X					
awaitable-error@0.1.0									X					
backon@1.2.0		X												
backtrace@0.3.74		X							X					
base64@0.21.7		X							X					
base64@0.22.1		X							X					
base64ct@1.6.0		X							X					
bb8@0.8.6									X					
bitflags@2.6.0		X							X					
block-buffer@0.10.4		X							X					
block-padding@0.3.3		X							X					
bumpalo@3.16.0		X							X					
byteorder@1.5.0									X				X	
bytes@1.8.0									X					
cbc@0.1.2		X							X					
cc@1.1.31		X							X					
cfg-if@1.0.0		X							X					
chrono@0.4.38		X							X					
cipher@0.4.4		X							X					
concurrent_arena@0.1.10									X					
const-oid@0.9.6		X							X					
const-random@0.1.18		X							X					
const-random-macro@0.1.16		X							X					
core-foundation-sys@0.8.7		X							X					
cpufeatures@0.2.14		X							X					
crc32c@0.6.8		X							X					
crunchy@0.2.2									X					
crypto-common@0.1.6		X							X					
der@0.7.9		X							X					
deranged@0.3.11		X							X					
derive_destructure2@0.1.3		X							X					
digest@0.10.7		X							X					
dlv-list@0.5.2		X							X					
errno@0.3.9		X							X					
fastrand@2.1.1		X							X					
flagset@0.4.6		X												
fnv@1.0.7		X							X					
form_urlencoded@1.2.1		X							X					
futures@0.3.31		X							X					
futures-channel@0.3.31		X							X					
futures-core@0.3.31		X							X					
futures-executor@0.3.31		X							X					
futures-io@0.3.31		X							X					
futures-macro@0.3.31		X							X					
futures-sink@0.3.31		X							X					
futures-task@0.3.31		X							X					
futures-util@0.3.31		X							X					
generic-array@0.14.7									X					
getrandom@0.2.15		X							X					
gimli@0.31.1		X							X					
gloo-timers@0.3.0		X							X					
hashbrown@0.14.5		X							X					
heck@0.5.0		X							X					
hermit-abi@0.3.9		X							X					
hex@0.4.3		X							X					
hmac@0.12.1		X							X					
home@0.5.9		X							X					
http@1.1.0		X							X					
http-body@1.0.1									X					
http-body-util@0.1.2									X					
httparse@1.9.5		X							X					
hyper@1.5.0									X					
hyper-rustls@0.27.3		X						X	X					
hyper-util@0.1.9									X					
iana-time-zone@0.1.61		X							X					
iana-time-zone-haiku@0.1.2		X							X					
idna@0.5.0		X							X					
indoc@2.0.5		X							X					
inout@0.1.3		X							X					
ipnet@2.10.1		X							X					
itoa@1.0.11		X							X					
js-sys@0.3.72		X							X					
jsonwebtoken@9.3.0									X					
lazy_static@1.5.0		X							X					
libc@0.2.161		X							X					
libm@0.2.10		X							X					
linux-raw-sys@0.4.14		X	X						X					
lock_api@0.4.12		X							X					
log@0.4.22		X							X					
md-5@0.10.6		X							X					
memchr@2.7.4									X				X	
memoffset@0.9.1									X					
mime@0.3.17		X							X					
miniz_oxide@0.8.0		X							X					X
mio@1.0.2									X					
num-bigint@0.4.6		X							X					
num-bigint-dig@0.8.4		X							X					
num-conv@0.1.0		X							X					
num-derive@0.3.3		X							X					
num-integer@0.1.46		X							X					
num-iter@0.1.45		X							X					
num-traits@0.2.19		X							X					
object@0.36.5		X							X					
once_cell@1.20.2		X							X					
opendal@0.50.2		X												
opendal-python@0.45.12		X												
openssh@0.11.2		X							X					
openssh-sftp-client@0.15.1									X					
openssh-sftp-client-lowlevel@0.7.0									X					
openssh-sftp-error@0.5.0									X					
openssh-sftp-protocol@0.24.0									X					
openssh-sftp-protocol-error@0.1.0									X					
ordered-multimap@0.7.3									X					
parking_lot@0.12.3		X							X					
parking_lot_core@0.9.10		X							X					
pbkdf2@0.12.2		X							X					
pem@3.0.4									X					
pem-rfc7468@0.7.0		X							X					
percent-encoding@2.3.1		X							X					
pin-project@1.1.7		X							X					
pin-project-internal@1.1.7		X							X					
pin-project-lite@0.2.15		X							X					
pin-utils@0.1.0		X							X					
pkcs1@0.7.5		X							X					
pkcs5@0.7.1		X							X					
pkcs8@0.10.2		X							X					
portable-atomic@1.9.0		X							X					
powerfmt@0.2.0		X							X					
ppv-lite86@0.2.20		X							X					
proc-macro2@1.0.89		X							X					
pyo3@0.22.5		X							X					
pyo3-async-runtimes@0.22.0		X												
pyo3-build-config@0.22.5		X							X					
pyo3-ffi@0.22.5		X							X					
pyo3-macros@0.22.5		X							X					
pyo3-macros-backend@0.22.5		X							X					
quick-xml@0.35.0									X					
quick-xml@0.36.2									X					
quote@1.0.37		X							X					
rand@0.8.5		X							X					
rand_chacha@0.3.1		X							X					
rand_core@0.6.4		X							X					
redox_syscall@0.5.7									X					
reqsign@0.16.1		X												
reqwest@0.12.8		X							X					
ring@0.17.8											X			
rsa@0.9.6		X							X					
rust-ini@0.21.1									X					
rustc-demangle@0.1.24		X							X					
rustc_version@0.4.1		X							X					
rustix@0.38.38		X	X						X					
rustls@0.23.15		X						X	X					
rustls-pemfile@2.2.0		X						X	X					
rustls-pki-types@1.10.0		X							X					
rustls-webpki@0.102.8								X						
ryu@1.0.18		X				X								
salsa20@0.10.2		X							X					
scopeguard@1.2.0		X							X					
scrypt@0.11.0		X							X					
semver@1.0.23		X							X					
serde@1.0.213		X							X					
serde_derive@1.0.213		X							X					
serde_json@1.0.132		X							X					
serde_urlencoded@0.7.1		X							X					
sha1@0.10.6		X							X					
sha2@0.10.8		X							X					
shell-escape@0.1.5		X							X					
shlex@1.3.0		X							X					
signal-hook-registry@1.4.2		X							X					
signature@2.2.0		X							X					
simple_asn1@0.6.2								X						
slab@0.4.9									X					
smallvec@1.13.2		X							X					
socket2@0.5.7		X							X					
spin@0.9.8									X					
spki@0.7.3		X							X					
ssh_format@0.14.1									X					
ssh_format_error@0.1.0									X					
stable_deref_trait@1.2.0		X							X					
subtle@2.6.1					X									
syn@1.0.109		X							X					
syn@2.0.85		X							X					
sync_wrapper@1.0.1		X												
target-lexicon@0.12.16			X											
tempfile@3.13.0		X							X					
thin-vec@0.2.13		X							X					
thiserror@1.0.65		X							X					
thiserror-impl@1.0.65		X							X					
time@0.3.36		X							X					
time-core@0.1.2		X							X					
time-macros@0.2.18		X							X					
tiny-keccak@2.0.2							X							
tinyvec@1.8.0		X							X					X
tinyvec_macros@0.1.1		X							X					X
tokio@1.41.0									X					
tokio-io-utility@0.7.6									X					
tokio-macros@2.4.0									X					
tokio-rustls@0.26.0		X							X					
tokio-util@0.7.12									X					
tower-service@0.3.3									X					
tracing@0.1.40									X					
tracing-attributes@0.1.27									X					
tracing-core@0.1.32									X					
trim-in-place@0.1.7									X					
triomphe@0.1.11		X							X					
try-lock@0.2.5									X					
typenum@1.17.0		X							X					
unicode-bidi@0.3.17		X							X					
unicode-ident@1.0.13		X							X			X		
unicode-normalization@0.1.24		X							X					
unindent@0.2.3		X							X					
untrusted@0.9.0								X						
url@2.5.2		X							X					
uuid@1.11.0		X							X					
vec-strings@0.4.8									X					
version_check@0.9.5		X							X					
want@0.3.1									X					
wasi@0.11.0+wasi-snapshot-preview1		X	X						X					
wasm-bindgen@0.2.95		X							X					
wasm-bindgen-backend@0.2.95		X							X					
wasm-bindgen-futures@0.4.45		X							X					
wasm-bindgen-macro@0.2.95		X							X					
wasm-bindgen-macro-support@0.2.95		X							X					
wasm-bindgen-shared@0.2.95		X							X					
wasm-streams@0.4.2		X							X					
web-sys@0.3.72		X							X					
webpki-roots@0.26.6										X				
windows-core@0.52.0		X							X					
windows-registry@0.2.0		X							X					
windows-result@0.2.0		X							X					
windows-strings@0.1.0		X							X					
windows-sys@0.52.0		X							X					
windows-sys@0.59.0		X							X					
windows-targets@0.52.6		X							X					
windows_aarch64_gnullvm@0.52.6		X							X					
windows_aarch64_msvc@0.52.6		X							X					
windows_i686_gnu@0.52.6		X							X					
windows_i686_gnullvm@0.52.6		X							X					
windows_i686_msvc@0.52.6		X							X					
windows_x86_64_gnu@0.52.6		X							X					
windows_x86_64_gnullvm@0.52.6		X							X					
windows_x86_64_msvc@0.52.6		X							X					
zerocopy@0.7.35		X		X					X					
zerocopy-derive@0.7.35		X		X					X					
zeroize@1.8.1		X							X					
