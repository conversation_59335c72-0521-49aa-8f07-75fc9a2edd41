(lang dune 2.1)
; Licensed to the Apache Software Foundation (ASF) under one
; or more contributor license agreements.  See the NOTICE file
; distributed with this work for additional information
; regarding copyright ownership.  The ASF licenses this file
; to you under the Apache License, Version 2.0 (the
; "License"); you may not use this file except in compliance
; with the License.  You may obtain a copy of the License at
;
;   http://www.apache.org/licenses/LICENSE-2.0
;
; Unless required by applicable law or agreed to in writing,
; software distributed under the License is distributed on an
; "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
; KIND, either express or implied.  See the License for the
; specific language governing permissions and limitations
; under the License.

(name opendal)
(generate_opam_files true)
(license Apache-2.0)
(maintainers "OpenDAL Contributors <<EMAIL>>")
(authors "OpenDAL Contributors <<EMAIL>>")
(source (github apache/opendal))
(documentation "https://opendal.apache.org/")
(package
 (name opendal)
 (synopsis "Apache OpenDAL™ OCaml Binding")
 (description
  "Apache OpenDAL™ is a data access layer that allows users to easily and efficiently retrieve data from various storage services in a unified way.")
 (depends
  (ounit2 (and (>= 2.2.6) :with-test))
  (ocaml (and (>= "4.10.0") (< 5)))
  (dune (>= "2.1"))
  conf-rust))
