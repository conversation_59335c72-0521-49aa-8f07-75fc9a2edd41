# This file is generated by dune, edit dune-project instead
opam-version: "2.0"
synopsis: "Apache OpenDAL™ OCaml Binding"
description:
  "Apache OpenDAL™ is a data access layer that allows users to easily and efficiently retrieve data from various storage services in a unified way."
maintainer: ["OpenDAL Contributors <<EMAIL>>"]
authors: ["OpenDAL Contributors <<EMAIL>>"]
license: "Apache-2.0"
homepage: "https://github.com/apache/opendal"
doc: "https://opendal.apache.org/"
bug-reports: "https://github.com/apache/opendal/issues"
depends: [
  "ounit2" {>= "2.2.6" & with-test}
  "ocaml" {>= "4.10.0" & < "5"}
  "dune" {>= "2.1"}
  "conf-rust"
]
build: [
  ["dune" "subst"] {pinned}
  [
    "dune"
    "build"
    "-p"
    name
    "-j"
    jobs
    "@install"
    "@runtest" {with-test}
    "@doc" {with-doc}
  ]
]
dev-repo: "git+https://github.com/apache/opendal.git"
