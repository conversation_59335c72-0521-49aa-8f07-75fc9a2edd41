; Licensed to the Apache Software Foundation (ASF) under one
; or more contributor license agreements.  See the NOTICE file
; distributed with this work for additional information
; regarding copyright ownership.  The ASF licenses this file
; to you under the Apache License, Version 2.0 (the
; "License"); you may not use this file except in compliance
; with the License.  You may obtain a copy of the License at
;
;   http://www.apache.org/licenses/LICENSE-2.0
;
; Unless required by applicable law or agreed to in writing,
; software distributed under the License is distributed on an
; "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
; KIND, either express or implied.  See the License for the
; specific language governing permissions and limitations
; under the License.

(rule
 (targets libopendal_core.a dllopendal_core.so)
 (deps
  (glob_files *.rs))
 (action
  (progn
   (run sh -c "cd %{project_root}/../.. && cargo build --release")
   (run sh -c
     "mv %{project_root}/../../target/release/libopendal_ocaml.so ./dllopendal_core.so 2> /dev/null || mv %{project_root}/../../target/release/libopendal_ocaml.dylib ./dllopendal_core.so")
   (run mv %{project_root}/../../target/release/libopendal_ocaml.a
     libopendal_core.a))))

(library
 (name opendal_core)
 (public_name opendal.opendal_core)
 (foreign_archives opendal_core)
 (c_library_flags
  (-lpthread -lc -lm)))
