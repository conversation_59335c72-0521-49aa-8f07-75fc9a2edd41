(* Generated by ocaml-rs *)

open! Bigarray

(* file: mod.rs *)

type seek_from = 
| Start of int64  (** 
    [Start]: Sets the offset to the provided number of bytes.
    *)

| End of int64 (** 
    [End]: Sets the offset to the size of this object plus the specified number of
    bytes.

    It is possible to seek beyond the end of an object, but it's an error to
    seek before byte 0.
    *)

| Current of int64 (** 
    [Current]: Sets the offset to the current position plus the specified number of
    bytes.

    It is possible to seek beyond the end of an object, but it's an error to
    seek before byte 0.  
    *)

