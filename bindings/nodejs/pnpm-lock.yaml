lockfileVersion: '6.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

devDependencies:
  '@aws-sdk/client-s3':
    specifier: ^3.468.0
    version: 3.468.0
  '@napi-rs/cli':
    specifier: ^2.18.3
    version: 2.18.3
  '@swc-node/register':
    specifier: ^1.6.2
    version: 1.6.8(@swc/core@1.3.100)(typescript@5.3.3)
  '@swc/core':
    specifier: ^1.3.38
    version: 1.3.100
  '@types/node':
    specifier: ^18.14.5
    version: 18.19.3
  '@types/react':
    specifier: ^18.2.48
    version: 18.2.48
  benny:
    specifier: ^3.7.1
    version: 3.7.1
  dotenv:
    specifier: ^16.0.3
    version: 16.3.1
  prettier:
    specifier: ^2.8.4
    version: 2.8.8
  typedoc:
    specifier: ^0.25
    version: 0.25.4(typescript@5.3.3)
  typescript:
    specifier: ^5.0.2
    version: 5.3.3
  vitest:
    specifier: ^1.6.0
    version: 1.6.0(@types/node@18.19.3)

packages:

  /@arrows/array@1.4.1:
    resolution: {integrity: sha512-MGYS8xi3c4tTy1ivhrVntFvufoNzje0PchjEz6G/SsWRgUKxL4tKwS6iPdO8vsaJYldagAeWMd5KRD0aX3Q39g==}
    dependencies:
      '@arrows/composition': 1.2.2
    dev: true

  /@arrows/composition@1.2.2:
    resolution: {integrity: sha512-9fh1yHwrx32lundiB3SlZ/VwuStPB4QakPsSLrGJFH6rCXvdrd060ivAZ7/2vlqPnEjBkPRRXOcG1YOu19p2GQ==}
    dev: true

  /@arrows/dispatch@1.0.3:
    resolution: {integrity: sha512-v/HwvrFonitYZM2PmBlAlCqVqxrkIIoiEuy5bQgn0BdfvlL0ooSBzcPzTMrtzY8eYktPyYcHg8fLbSgyybXEqw==}
    dependencies:
      '@arrows/composition': 1.2.2
    dev: true

  /@arrows/error@1.0.2:
    resolution: {integrity: sha512-yvkiv1ay4Z3+Z6oQsUkedsQm5aFdyPpkBUQs8vejazU/RmANABx6bMMcBPPHI4aW43VPQmXFfBzr/4FExwWTEA==}
    dev: true

  /@arrows/multimethod@1.4.1:
    resolution: {integrity: sha512-AZnAay0dgPnCJxn3We5uKiB88VL+1ZIF2SjZohLj6vqY2UyvB/sKdDnFP+LZNVsTC5lcnGPmLlRRkAh4sXkXsQ==}
    dependencies:
      '@arrows/array': 1.4.1
      '@arrows/composition': 1.2.2
      '@arrows/error': 1.0.2
      fast-deep-equal: 3.1.3
    dev: true

  /@aws-crypto/crc32@3.0.0:
    resolution: {integrity: sha512-IzSgsrxUcsrejQbPVilIKy16kAT52EwB6zSaI+M3xxIhKh5+aldEyvI+z6erM7TCLB2BJsFrtHjp6/4/sr+3dA==}
    dependencies:
      '@aws-crypto/util': 3.0.0
      '@aws-sdk/types': 3.468.0
      tslib: 1.14.1
    dev: true

  /@aws-crypto/crc32c@3.0.0:
    resolution: {integrity: sha512-ENNPPManmnVJ4BTXlOjAgD7URidbAznURqD0KvfREyc4o20DPYdEldU1f5cQ7Jbj0CJJSPaMIk/9ZshdB3210w==}
    dependencies:
      '@aws-crypto/util': 3.0.0
      '@aws-sdk/types': 3.468.0
      tslib: 1.14.1
    dev: true

  /@aws-crypto/ie11-detection@3.0.0:
    resolution: {integrity: sha512-341lBBkiY1DfDNKai/wXM3aujNBkXR7tq1URPQDL9wi3AUbI80NR74uF1TXHMm7po1AcnFk8iu2S2IeU/+/A+Q==}
    dependencies:
      tslib: 1.14.1
    dev: true

  /@aws-crypto/sha1-browser@3.0.0:
    resolution: {integrity: sha512-NJth5c997GLHs6nOYTzFKTbYdMNA6/1XlKVgnZoaZcQ7z7UJlOgj2JdbHE8tiYLS3fzXNCguct77SPGat2raSw==}
    dependencies:
      '@aws-crypto/ie11-detection': 3.0.0
      '@aws-crypto/supports-web-crypto': 3.0.0
      '@aws-crypto/util': 3.0.0
      '@aws-sdk/types': 3.468.0
      '@aws-sdk/util-locate-window': 3.465.0
      '@aws-sdk/util-utf8-browser': 3.259.0
      tslib: 1.14.1
    dev: true

  /@aws-crypto/sha256-browser@3.0.0:
    resolution: {integrity: sha512-8VLmW2B+gjFbU5uMeqtQM6Nj0/F1bro80xQXCW6CQBWgosFWXTx77aeOF5CAIAmbOK64SdMBJdNr6J41yP5mvQ==}
    dependencies:
      '@aws-crypto/ie11-detection': 3.0.0
      '@aws-crypto/sha256-js': 3.0.0
      '@aws-crypto/supports-web-crypto': 3.0.0
      '@aws-crypto/util': 3.0.0
      '@aws-sdk/types': 3.468.0
      '@aws-sdk/util-locate-window': 3.465.0
      '@aws-sdk/util-utf8-browser': 3.259.0
      tslib: 1.14.1
    dev: true

  /@aws-crypto/sha256-js@3.0.0:
    resolution: {integrity: sha512-PnNN7os0+yd1XvXAy23CFOmTbMaDxgxXtTKHybrJ39Y8kGzBATgBFibWJKH6BhytLI/Zyszs87xCOBNyBig6vQ==}
    dependencies:
      '@aws-crypto/util': 3.0.0
      '@aws-sdk/types': 3.468.0
      tslib: 1.14.1
    dev: true

  /@aws-crypto/supports-web-crypto@3.0.0:
    resolution: {integrity: sha512-06hBdMwUAb2WFTuGG73LSC0wfPu93xWwo5vL2et9eymgmu3Id5vFAHBbajVWiGhPO37qcsdCap/FqXvJGJWPIg==}
    dependencies:
      tslib: 1.14.1
    dev: true

  /@aws-crypto/util@3.0.0:
    resolution: {integrity: sha512-2OJlpeJpCR48CC8r+uKVChzs9Iungj9wkZrl8Z041DWEWvyIHILYKCPNzJghKsivj+S3mLo6BVc7mBNzdxA46w==}
    dependencies:
      '@aws-sdk/types': 3.468.0
      '@aws-sdk/util-utf8-browser': 3.259.0
      tslib: 1.14.1
    dev: true

  /@aws-sdk/client-s3@3.468.0:
    resolution: {integrity: sha512-j0MnSYKu7KRAPXXn5egmJBzzPAgM/Hb0UUr0CHRrj8eMV7Ni/cZQpbU8tqgFel7BrsS4YINB5W/Q3FShHpI/8w==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@aws-crypto/sha1-browser': 3.0.0
      '@aws-crypto/sha256-browser': 3.0.0
      '@aws-crypto/sha256-js': 3.0.0
      '@aws-sdk/client-sts': 3.468.0
      '@aws-sdk/core': 3.468.0
      '@aws-sdk/credential-provider-node': 3.468.0
      '@aws-sdk/middleware-bucket-endpoint': 3.468.0
      '@aws-sdk/middleware-expect-continue': 3.468.0
      '@aws-sdk/middleware-flexible-checksums': 3.468.0
      '@aws-sdk/middleware-host-header': 3.468.0
      '@aws-sdk/middleware-location-constraint': 3.468.0
      '@aws-sdk/middleware-logger': 3.468.0
      '@aws-sdk/middleware-recursion-detection': 3.468.0
      '@aws-sdk/middleware-sdk-s3': 3.468.0
      '@aws-sdk/middleware-signing': 3.468.0
      '@aws-sdk/middleware-ssec': 3.468.0
      '@aws-sdk/middleware-user-agent': 3.468.0
      '@aws-sdk/region-config-resolver': 3.468.0
      '@aws-sdk/signature-v4-multi-region': 3.468.0
      '@aws-sdk/types': 3.468.0
      '@aws-sdk/util-endpoints': 3.468.0
      '@aws-sdk/util-user-agent-browser': 3.468.0
      '@aws-sdk/util-user-agent-node': 3.468.0
      '@aws-sdk/xml-builder': 3.465.0
      '@smithy/config-resolver': 2.0.21
      '@smithy/eventstream-serde-browser': 2.0.15
      '@smithy/eventstream-serde-config-resolver': 2.0.15
      '@smithy/eventstream-serde-node': 2.0.15
      '@smithy/fetch-http-handler': 2.3.1
      '@smithy/hash-blob-browser': 2.0.16
      '@smithy/hash-node': 2.0.17
      '@smithy/hash-stream-node': 2.0.17
      '@smithy/invalid-dependency': 2.0.15
      '@smithy/md5-js': 2.0.17
      '@smithy/middleware-content-length': 2.0.17
      '@smithy/middleware-endpoint': 2.2.3
      '@smithy/middleware-retry': 2.0.24
      '@smithy/middleware-serde': 2.0.15
      '@smithy/middleware-stack': 2.0.9
      '@smithy/node-config-provider': 2.1.8
      '@smithy/node-http-handler': 2.2.1
      '@smithy/protocol-http': 3.0.11
      '@smithy/smithy-client': 2.1.18
      '@smithy/types': 2.7.0
      '@smithy/url-parser': 2.0.15
      '@smithy/util-base64': 2.0.1
      '@smithy/util-body-length-browser': 2.0.1
      '@smithy/util-body-length-node': 2.1.0
      '@smithy/util-defaults-mode-browser': 2.0.22
      '@smithy/util-defaults-mode-node': 2.0.29
      '@smithy/util-endpoints': 1.0.7
      '@smithy/util-retry': 2.0.8
      '@smithy/util-stream': 2.0.23
      '@smithy/util-utf8': 2.0.2
      '@smithy/util-waiter': 2.0.15
      fast-xml-parser: 4.2.5
      tslib: 2.6.2
    transitivePeerDependencies:
      - aws-crt
    dev: true

  /@aws-sdk/client-sso@3.468.0:
    resolution: {integrity: sha512-NabkDaiFsMP8lBR3+JzdtOVarH8kCJst30fQyBIs2PI0uMfajFJ+SK9JTg1J1YZY6aNJBxo2Bxu3dl0fjZ5N/g==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@aws-crypto/sha256-browser': 3.0.0
      '@aws-crypto/sha256-js': 3.0.0
      '@aws-sdk/core': 3.468.0
      '@aws-sdk/middleware-host-header': 3.468.0
      '@aws-sdk/middleware-logger': 3.468.0
      '@aws-sdk/middleware-recursion-detection': 3.468.0
      '@aws-sdk/middleware-user-agent': 3.468.0
      '@aws-sdk/region-config-resolver': 3.468.0
      '@aws-sdk/types': 3.468.0
      '@aws-sdk/util-endpoints': 3.468.0
      '@aws-sdk/util-user-agent-browser': 3.468.0
      '@aws-sdk/util-user-agent-node': 3.468.0
      '@smithy/config-resolver': 2.0.21
      '@smithy/fetch-http-handler': 2.3.1
      '@smithy/hash-node': 2.0.17
      '@smithy/invalid-dependency': 2.0.15
      '@smithy/middleware-content-length': 2.0.17
      '@smithy/middleware-endpoint': 2.2.3
      '@smithy/middleware-retry': 2.0.24
      '@smithy/middleware-serde': 2.0.15
      '@smithy/middleware-stack': 2.0.9
      '@smithy/node-config-provider': 2.1.8
      '@smithy/node-http-handler': 2.2.1
      '@smithy/protocol-http': 3.0.11
      '@smithy/smithy-client': 2.1.18
      '@smithy/types': 2.7.0
      '@smithy/url-parser': 2.0.15
      '@smithy/util-base64': 2.0.1
      '@smithy/util-body-length-browser': 2.0.1
      '@smithy/util-body-length-node': 2.1.0
      '@smithy/util-defaults-mode-browser': 2.0.22
      '@smithy/util-defaults-mode-node': 2.0.29
      '@smithy/util-endpoints': 1.0.7
      '@smithy/util-retry': 2.0.8
      '@smithy/util-utf8': 2.0.2
      tslib: 2.6.2
    transitivePeerDependencies:
      - aws-crt
    dev: true

  /@aws-sdk/client-sts@3.468.0:
    resolution: {integrity: sha512-EausH7ezv1AIgl/4rfZRNRxrFND5hChbIqkuAf8e5wZ74HUEVBMmD5Jiwfs0WRCso3ejOjsNtS8PAOA3djn28w==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@aws-crypto/sha256-browser': 3.0.0
      '@aws-crypto/sha256-js': 3.0.0
      '@aws-sdk/core': 3.468.0
      '@aws-sdk/credential-provider-node': 3.468.0
      '@aws-sdk/middleware-host-header': 3.468.0
      '@aws-sdk/middleware-logger': 3.468.0
      '@aws-sdk/middleware-recursion-detection': 3.468.0
      '@aws-sdk/middleware-sdk-sts': 3.468.0
      '@aws-sdk/middleware-signing': 3.468.0
      '@aws-sdk/middleware-user-agent': 3.468.0
      '@aws-sdk/region-config-resolver': 3.468.0
      '@aws-sdk/types': 3.468.0
      '@aws-sdk/util-endpoints': 3.468.0
      '@aws-sdk/util-user-agent-browser': 3.468.0
      '@aws-sdk/util-user-agent-node': 3.468.0
      '@smithy/config-resolver': 2.0.21
      '@smithy/fetch-http-handler': 2.3.1
      '@smithy/hash-node': 2.0.17
      '@smithy/invalid-dependency': 2.0.15
      '@smithy/middleware-content-length': 2.0.17
      '@smithy/middleware-endpoint': 2.2.3
      '@smithy/middleware-retry': 2.0.24
      '@smithy/middleware-serde': 2.0.15
      '@smithy/middleware-stack': 2.0.9
      '@smithy/node-config-provider': 2.1.8
      '@smithy/node-http-handler': 2.2.1
      '@smithy/protocol-http': 3.0.11
      '@smithy/smithy-client': 2.1.18
      '@smithy/types': 2.7.0
      '@smithy/url-parser': 2.0.15
      '@smithy/util-base64': 2.0.1
      '@smithy/util-body-length-browser': 2.0.1
      '@smithy/util-body-length-node': 2.1.0
      '@smithy/util-defaults-mode-browser': 2.0.22
      '@smithy/util-defaults-mode-node': 2.0.29
      '@smithy/util-endpoints': 1.0.7
      '@smithy/util-retry': 2.0.8
      '@smithy/util-utf8': 2.0.2
      fast-xml-parser: 4.2.5
      tslib: 2.6.2
    transitivePeerDependencies:
      - aws-crt
    dev: true

  /@aws-sdk/core@3.468.0:
    resolution: {integrity: sha512-ezUJR9VvknKoXzNZ4wvzGi1jdkmm+/1dUYQ9Sw4r8bzlJDTsUnWbyvaDlBQh81RuhLtVkaUfTnQKoec0cwlZKQ==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@smithy/smithy-client': 2.1.18
      tslib: 2.6.2
    dev: true

  /@aws-sdk/credential-provider-env@3.468.0:
    resolution: {integrity: sha512-k/1WHd3KZn0EQYjadooj53FC0z24/e4dUZhbSKTULgmxyO62pwh9v3Brvw4WRa/8o2wTffU/jo54tf4vGuP/ZA==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@aws-sdk/types': 3.468.0
      '@smithy/property-provider': 2.0.16
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@aws-sdk/credential-provider-ini@3.468.0:
    resolution: {integrity: sha512-DBYsptYBq0xC+GTh+3dN3Q9/wRZiPpsHA4yCC1mskEbJfMy7EIZZKtZ8lOkZ24NOI5oea4o3L+wFTxOeFSKntA==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@aws-sdk/credential-provider-env': 3.468.0
      '@aws-sdk/credential-provider-process': 3.468.0
      '@aws-sdk/credential-provider-sso': 3.468.0
      '@aws-sdk/credential-provider-web-identity': 3.468.0
      '@aws-sdk/types': 3.468.0
      '@smithy/credential-provider-imds': 2.1.4
      '@smithy/property-provider': 2.0.16
      '@smithy/shared-ini-file-loader': 2.2.7
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    transitivePeerDependencies:
      - aws-crt
    dev: true

  /@aws-sdk/credential-provider-node@3.468.0:
    resolution: {integrity: sha512-iZlWWZXp6zAH4sP3VrqF7RpAmzl8Qr8tuVkF7ubUZhzyWzKfhLVzqRJqbMYCBPGmfZLAZWjsziPHaBErYkG/5g==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@aws-sdk/credential-provider-env': 3.468.0
      '@aws-sdk/credential-provider-ini': 3.468.0
      '@aws-sdk/credential-provider-process': 3.468.0
      '@aws-sdk/credential-provider-sso': 3.468.0
      '@aws-sdk/credential-provider-web-identity': 3.468.0
      '@aws-sdk/types': 3.468.0
      '@smithy/credential-provider-imds': 2.1.4
      '@smithy/property-provider': 2.0.16
      '@smithy/shared-ini-file-loader': 2.2.7
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    transitivePeerDependencies:
      - aws-crt
    dev: true

  /@aws-sdk/credential-provider-process@3.468.0:
    resolution: {integrity: sha512-OYSn1A/UsyPJ7Z8Q2cNhTf55O36shPmSsvOfND04nSfu1nPaR+VUvvsP7v+brhGpwC/GAKTIdGAo4blH31BS6A==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@aws-sdk/types': 3.468.0
      '@smithy/property-provider': 2.0.16
      '@smithy/shared-ini-file-loader': 2.2.7
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@aws-sdk/credential-provider-sso@3.468.0:
    resolution: {integrity: sha512-eIdGoIw10xyBm7TDcV5Y/W7tzNs2f4H+2G5ZdjG2XGLAELsKCoixe+9ZB662MLtLCxvm7eE1GjOjKsSua6MImQ==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@aws-sdk/client-sso': 3.468.0
      '@aws-sdk/token-providers': 3.468.0
      '@aws-sdk/types': 3.468.0
      '@smithy/property-provider': 2.0.16
      '@smithy/shared-ini-file-loader': 2.2.7
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    transitivePeerDependencies:
      - aws-crt
    dev: true

  /@aws-sdk/credential-provider-web-identity@3.468.0:
    resolution: {integrity: sha512-rexymPmXjtkwCPfhnUq3EjO1rSkf39R4Jz9CqiM7OsqK2qlT5Y/V3gnMKn0ZMXsYaQOMfM3cT5xly5R+OKDHlw==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@aws-sdk/types': 3.468.0
      '@smithy/property-provider': 2.0.16
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@aws-sdk/middleware-bucket-endpoint@3.468.0:
    resolution: {integrity: sha512-Dak7sSaPxkTWuBzvFI0zXL1t+/6JdeZZVLRckp2reoQ46CY/hnCbd5/wITtO7CYyjHX7WrEILjTynfZoa1E7Qw==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@aws-sdk/types': 3.468.0
      '@aws-sdk/util-arn-parser': 3.465.0
      '@smithy/node-config-provider': 2.1.8
      '@smithy/protocol-http': 3.0.11
      '@smithy/types': 2.7.0
      '@smithy/util-config-provider': 2.0.0
      tslib: 2.6.2
    dev: true

  /@aws-sdk/middleware-expect-continue@3.468.0:
    resolution: {integrity: sha512-/wmLjmfgeulxhhmnxX3X3N933TvGsYckVIFjAtDSpLjqkbwzEcNiLq7AdmNJ4BfxG0MCMgcht561DCCD19x8Bg==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@aws-sdk/types': 3.468.0
      '@smithy/protocol-http': 3.0.11
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@aws-sdk/middleware-flexible-checksums@3.468.0:
    resolution: {integrity: sha512-LQwL/N5MCj3Y5keLLewHTqeAXUIMsHFZyxDXRm/uxrOon9ufLKDvGvzAmfwn1/CuSUo66ZfT8VPSA4BsC90RtA==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@aws-crypto/crc32': 3.0.0
      '@aws-crypto/crc32c': 3.0.0
      '@aws-sdk/types': 3.468.0
      '@smithy/is-array-buffer': 2.0.0
      '@smithy/protocol-http': 3.0.11
      '@smithy/types': 2.7.0
      '@smithy/util-utf8': 2.0.2
      tslib: 2.6.2
    dev: true

  /@aws-sdk/middleware-host-header@3.468.0:
    resolution: {integrity: sha512-gwQ+/QhX+lhof304r6zbZ/V5l5cjhGRxLL3CjH1uJPMcOAbw9wUlMdl+ibr8UwBZ5elfKFGiB1cdW/0uMchw0w==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@aws-sdk/types': 3.468.0
      '@smithy/protocol-http': 3.0.11
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@aws-sdk/middleware-location-constraint@3.468.0:
    resolution: {integrity: sha512-0gBX/lDynQr4YIhM9h1dVnkVWqrg+34iOCVIUq8jHxzUzgZWglGkG9lHGGg0r1xkLTmegeoo1OKH8wrQ6n33Cg==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@aws-sdk/types': 3.468.0
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@aws-sdk/middleware-logger@3.468.0:
    resolution: {integrity: sha512-X5XHKV7DHRXI3f29SAhJPe/OxWRFgDWDMMCALfzhmJfCi6Jfh0M14cJKoC+nl+dk9lB+36+jKjhjETZaL2bPlA==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@aws-sdk/types': 3.468.0
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@aws-sdk/middleware-recursion-detection@3.468.0:
    resolution: {integrity: sha512-vch9IQib2Ng9ucSyRW2eKNQXHUPb5jUPCLA5otTW/8nGjcOU37LxQG4WrxO7uaJ9Oe8hjHO+hViE3P0KISUhtA==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@aws-sdk/types': 3.468.0
      '@smithy/protocol-http': 3.0.11
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@aws-sdk/middleware-sdk-s3@3.468.0:
    resolution: {integrity: sha512-8Ma8tdHYH0stMmGQHh/8eI53oAfiuUJvnQdILWcNArAwlVXt+DJirCSGWP8SqvYdKGa4+jr1YW3+nTdhnm2FZg==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@aws-sdk/types': 3.468.0
      '@aws-sdk/util-arn-parser': 3.465.0
      '@smithy/node-config-provider': 2.1.8
      '@smithy/protocol-http': 3.0.11
      '@smithy/signature-v4': 2.0.17
      '@smithy/smithy-client': 2.1.18
      '@smithy/types': 2.7.0
      '@smithy/util-config-provider': 2.0.0
      tslib: 2.6.2
    dev: true

  /@aws-sdk/middleware-sdk-sts@3.468.0:
    resolution: {integrity: sha512-xRy8NKfHbmafHwdbotdWgHBvRs0YZgk20GrhFJKp43bkqVbJ5bNlh3nQXf1DeFY9fARR84Bfotya4fwCUHWgZg==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@aws-sdk/middleware-signing': 3.468.0
      '@aws-sdk/types': 3.468.0
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@aws-sdk/middleware-signing@3.468.0:
    resolution: {integrity: sha512-s+7fSB1gdnnTj5O0aCCarX3z5Vppop8kazbNSZADdkfHIDWCN80IH4ZNjY3OWqaAz0HmR4LNNrovdR304ojb4Q==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@aws-sdk/types': 3.468.0
      '@smithy/property-provider': 2.0.16
      '@smithy/protocol-http': 3.0.11
      '@smithy/signature-v4': 2.0.17
      '@smithy/types': 2.7.0
      '@smithy/util-middleware': 2.0.8
      tslib: 2.6.2
    dev: true

  /@aws-sdk/middleware-ssec@3.468.0:
    resolution: {integrity: sha512-y1qLW24wRkOGBTK5d6eJXf6d8HYo4rzT4a1mNDN1rd18NSffwQ6Yke5qeUiIaxa0y/l+FvvNYErbhYtij2rJoQ==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@aws-sdk/types': 3.468.0
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@aws-sdk/middleware-user-agent@3.468.0:
    resolution: {integrity: sha512-lmqaEChVWK6MvNpM/LH504pRsP3p/IuZugWwxCbelKw4bGVU4IgG3mbjfATiIlHo4rW8ttHh1bTsZIGjWOqNeA==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@aws-sdk/types': 3.468.0
      '@aws-sdk/util-endpoints': 3.468.0
      '@smithy/protocol-http': 3.0.11
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@aws-sdk/region-config-resolver@3.468.0:
    resolution: {integrity: sha512-EkDfaumuBhDJFg4lmvWiBE8Ln4BF6hYNC2YfkjKCTEuePy5BKryFedwylYZZ3CJG/uVyfr8xBy+mvoR8plpHjg==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@smithy/node-config-provider': 2.1.8
      '@smithy/types': 2.7.0
      '@smithy/util-config-provider': 2.0.0
      '@smithy/util-middleware': 2.0.8
      tslib: 2.6.2
    dev: true

  /@aws-sdk/signature-v4-multi-region@3.468.0:
    resolution: {integrity: sha512-ADMWVrqUUjaiWmK7IcBuekOd8nNW6qV1G8ZM9Dgu2U7ezC4gzgZ3IFqZRcQXANX32EC1K3EpDx6fhPpOE/Unbg==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@aws-sdk/middleware-sdk-s3': 3.468.0
      '@aws-sdk/types': 3.468.0
      '@smithy/protocol-http': 3.0.11
      '@smithy/signature-v4': 2.0.17
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@aws-sdk/token-providers@3.468.0:
    resolution: {integrity: sha512-IpLbthZmFXotwtgkE1Bw4HcKjwpAsGM+6iTXs4amZJqllJClOgyV/sV5Cze+8AqanfCZoPIFTmXyg8LfJTYwbw==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@aws-crypto/sha256-browser': 3.0.0
      '@aws-crypto/sha256-js': 3.0.0
      '@aws-sdk/middleware-host-header': 3.468.0
      '@aws-sdk/middleware-logger': 3.468.0
      '@aws-sdk/middleware-recursion-detection': 3.468.0
      '@aws-sdk/middleware-user-agent': 3.468.0
      '@aws-sdk/region-config-resolver': 3.468.0
      '@aws-sdk/types': 3.468.0
      '@aws-sdk/util-endpoints': 3.468.0
      '@aws-sdk/util-user-agent-browser': 3.468.0
      '@aws-sdk/util-user-agent-node': 3.468.0
      '@smithy/config-resolver': 2.0.21
      '@smithy/fetch-http-handler': 2.3.1
      '@smithy/hash-node': 2.0.17
      '@smithy/invalid-dependency': 2.0.15
      '@smithy/middleware-content-length': 2.0.17
      '@smithy/middleware-endpoint': 2.2.3
      '@smithy/middleware-retry': 2.0.24
      '@smithy/middleware-serde': 2.0.15
      '@smithy/middleware-stack': 2.0.9
      '@smithy/node-config-provider': 2.1.8
      '@smithy/node-http-handler': 2.2.1
      '@smithy/property-provider': 2.0.16
      '@smithy/protocol-http': 3.0.11
      '@smithy/shared-ini-file-loader': 2.2.7
      '@smithy/smithy-client': 2.1.18
      '@smithy/types': 2.7.0
      '@smithy/url-parser': 2.0.15
      '@smithy/util-base64': 2.0.1
      '@smithy/util-body-length-browser': 2.0.1
      '@smithy/util-body-length-node': 2.1.0
      '@smithy/util-defaults-mode-browser': 2.0.22
      '@smithy/util-defaults-mode-node': 2.0.29
      '@smithy/util-endpoints': 1.0.7
      '@smithy/util-retry': 2.0.8
      '@smithy/util-utf8': 2.0.2
      tslib: 2.6.2
    transitivePeerDependencies:
      - aws-crt
    dev: true

  /@aws-sdk/types@3.468.0:
    resolution: {integrity: sha512-rx/9uHI4inRbp2tw3Y4Ih4PNZkVj32h7WneSg3MVgVjAoVD5Zti9KhS5hkvsBxfgmQmg0AQbE+b1sy5WGAgntA==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@aws-sdk/util-arn-parser@3.465.0:
    resolution: {integrity: sha512-zOJ82vzDJFqBX9yZBlNeHHrul/kpx/DCoxzW5UBbZeb26kfV53QhMSoEmY8/lEbBqlqargJ/sgRC845GFhHNQw==}
    engines: {node: '>=14.0.0'}
    dependencies:
      tslib: 2.6.2
    dev: true

  /@aws-sdk/util-endpoints@3.468.0:
    resolution: {integrity: sha512-P91EbMG2+1ZToJeTLaRkdO7qM7RI0svuMVLkIdHV9rHR7PeUKUWMpf46xh8rQsIjKC9Arf+I9ueWp3iHJt1T5w==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@aws-sdk/types': 3.468.0
      '@smithy/util-endpoints': 1.0.7
      tslib: 2.6.2
    dev: true

  /@aws-sdk/util-locate-window@3.465.0:
    resolution: {integrity: sha512-f+QNcWGswredzC1ExNAB/QzODlxwaTdXkNT5cvke2RLX8SFU5pYk6h4uCtWC0vWPELzOfMfloBrJefBzlarhsw==}
    engines: {node: '>=14.0.0'}
    dependencies:
      tslib: 2.6.2
    dev: true

  /@aws-sdk/util-user-agent-browser@3.468.0:
    resolution: {integrity: sha512-OJyhWWsDEizR3L+dCgMXSUmaCywkiZ7HSbnQytbeKGwokIhD69HTiJcibF/sgcM5gk4k3Mq3puUhGnEZ46GIig==}
    dependencies:
      '@aws-sdk/types': 3.468.0
      '@smithy/types': 2.7.0
      bowser: 2.11.0
      tslib: 2.6.2
    dev: true

  /@aws-sdk/util-user-agent-node@3.468.0:
    resolution: {integrity: sha512-9p+Zyp6xmJUkcryTNmQQwdhRK6gAC6zVEJZLomLGQhD7sWcCzstolw//mAS3AKVQFYWnCEGKrDJdgT0KObCf4g==}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      aws-crt: '>=1.0.0'
    peerDependenciesMeta:
      aws-crt:
        optional: true
    dependencies:
      '@aws-sdk/types': 3.468.0
      '@smithy/node-config-provider': 2.1.8
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@aws-sdk/util-utf8-browser@3.259.0:
    resolution: {integrity: sha512-UvFa/vR+e19XookZF8RzFZBrw2EUkQWxiBW0yYQAhvk3C+QVGl0H3ouca8LDBlBfQKXwmW3huo/59H8rwb1wJw==}
    dependencies:
      tslib: 2.6.2
    dev: true

  /@aws-sdk/xml-builder@3.465.0:
    resolution: {integrity: sha512-9TKW5ZgsReygePTnAUdvaqxr/k1HXsEz2yDnk/jTLaUeRPsd5la8fFjb6OfgYYlbEVNlxTcKzaqOdrqxpUkmyQ==}
    engines: {node: '>=14.0.0'}
    dependencies:
      tslib: 2.6.2
    dev: true

  /@esbuild/android-arm64@0.19.8:
    resolution: {integrity: sha512-B8JbS61bEunhfx8kasogFENgQfr/dIp+ggYXwTqdbMAgGDhRa3AaPpQMuQU0rNxDLECj6FhDzk1cF9WHMVwrtA==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/android-arm@0.19.8:
    resolution: {integrity: sha512-31E2lxlGM1KEfivQl8Yf5aYU/mflz9g06H6S15ITUFQueMFtFjESRMoDSkvMo8thYvLBax+VKTPlpnx+sPicOA==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/android-x64@0.19.8:
    resolution: {integrity: sha512-rdqqYfRIn4jWOp+lzQttYMa2Xar3OK9Yt2fhOhzFXqg0rVWEfSclJvZq5fZslnz6ypHvVf3CT7qyf0A5pM682A==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/darwin-arm64@0.19.8:
    resolution: {integrity: sha512-RQw9DemMbIq35Bprbboyf8SmOr4UXsRVxJ97LgB55VKKeJOOdvsIPy0nFyF2l8U+h4PtBx/1kRf0BelOYCiQcw==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/darwin-x64@0.19.8:
    resolution: {integrity: sha512-3sur80OT9YdeZwIVgERAysAbwncom7b4bCI2XKLjMfPymTud7e/oY4y+ci1XVp5TfQp/bppn7xLw1n/oSQY3/Q==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/freebsd-arm64@0.19.8:
    resolution: {integrity: sha512-WAnPJSDattvS/XtPCTj1tPoTxERjcTpH6HsMr6ujTT+X6rylVe8ggxk8pVxzf5U1wh5sPODpawNicF5ta/9Tmw==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/freebsd-x64@0.19.8:
    resolution: {integrity: sha512-ICvZyOplIjmmhjd6mxi+zxSdpPTKFfyPPQMQTK/w+8eNK6WV01AjIztJALDtwNNfFhfZLux0tZLC+U9nSyA5Zg==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-arm64@0.19.8:
    resolution: {integrity: sha512-z1zMZivxDLHWnyGOctT9JP70h0beY54xDDDJt4VpTX+iwA77IFsE1vCXWmprajJGa+ZYSqkSbRQ4eyLCpCmiCQ==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-arm@0.19.8:
    resolution: {integrity: sha512-H4vmI5PYqSvosPaTJuEppU9oz1dq2A7Mr2vyg5TF9Ga+3+MGgBdGzcyBP7qK9MrwFQZlvNyJrvz6GuCaj3OukQ==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-ia32@0.19.8:
    resolution: {integrity: sha512-1a8suQiFJmZz1khm/rDglOc8lavtzEMRo0v6WhPgxkrjcU0LkHj+TwBrALwoz/OtMExvsqbbMI0ChyelKabSvQ==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-loong64@0.19.8:
    resolution: {integrity: sha512-fHZWS2JJxnXt1uYJsDv9+b60WCc2RlvVAy1F76qOLtXRO+H4mjt3Tr6MJ5l7Q78X8KgCFudnTuiQRBhULUyBKQ==}
    engines: {node: '>=12'}
    cpu: [loong64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-mips64el@0.19.8:
    resolution: {integrity: sha512-Wy/z0EL5qZYLX66dVnEg9riiwls5IYnziwuju2oUiuxVc+/edvqXa04qNtbrs0Ukatg5HEzqT94Zs7J207dN5Q==}
    engines: {node: '>=12'}
    cpu: [mips64el]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-ppc64@0.19.8:
    resolution: {integrity: sha512-ETaW6245wK23YIEufhMQ3HSeHO7NgsLx8gygBVldRHKhOlD1oNeNy/P67mIh1zPn2Hr2HLieQrt6tWrVwuqrxg==}
    engines: {node: '>=12'}
    cpu: [ppc64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-riscv64@0.19.8:
    resolution: {integrity: sha512-T2DRQk55SgoleTP+DtPlMrxi/5r9AeFgkhkZ/B0ap99zmxtxdOixOMI570VjdRCs9pE4Wdkz7JYrsPvsl7eESg==}
    engines: {node: '>=12'}
    cpu: [riscv64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-s390x@0.19.8:
    resolution: {integrity: sha512-NPxbdmmo3Bk7mbNeHmcCd7R7fptJaczPYBaELk6NcXxy7HLNyWwCyDJ/Xx+/YcNH7Im5dHdx9gZ5xIwyliQCbg==}
    engines: {node: '>=12'}
    cpu: [s390x]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-x64@0.19.8:
    resolution: {integrity: sha512-lytMAVOM3b1gPypL2TRmZ5rnXl7+6IIk8uB3eLsV1JwcizuolblXRrc5ShPrO9ls/b+RTp+E6gbsuLWHWi2zGg==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/netbsd-x64@0.19.8:
    resolution: {integrity: sha512-hvWVo2VsXz/8NVt1UhLzxwAfo5sioj92uo0bCfLibB0xlOmimU/DeAEsQILlBQvkhrGjamP0/el5HU76HAitGw==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [netbsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/openbsd-x64@0.19.8:
    resolution: {integrity: sha512-/7Y7u77rdvmGTxR83PgaSvSBJCC2L3Kb1M/+dmSIvRvQPXXCuC97QAwMugBNG0yGcbEGfFBH7ojPzAOxfGNkwQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [openbsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/sunos-x64@0.19.8:
    resolution: {integrity: sha512-9Lc4s7Oi98GqFA4HzA/W2JHIYfnXbUYgekUP/Sm4BG9sfLjyv6GKKHKKVs83SMicBF2JwAX6A1PuOLMqpD001w==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [sunos]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/win32-arm64@0.19.8:
    resolution: {integrity: sha512-rq6WzBGjSzihI9deW3fC2Gqiak68+b7qo5/3kmB6Gvbh/NYPA0sJhrnp7wgV4bNwjqM+R2AApXGxMO7ZoGhIJg==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/win32-ia32@0.19.8:
    resolution: {integrity: sha512-AIAbverbg5jMvJznYiGhrd3sumfwWs8572mIJL5NQjJa06P8KfCPWZQ0NwZbPQnbQi9OWSZhFVSUWjjIrn4hSw==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/win32-x64@0.19.8:
    resolution: {integrity: sha512-bfZ0cQ1uZs2PqpulNL5j/3w+GDhP36k1K5c38QdQg+Swy51jFZWWeIkteNsufkQxp986wnqRRsb/bHbY1WQ7TA==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@jest/schemas@29.6.3:
    resolution: {integrity: sha512-mo5j5X+jIZmJQveBKeS/clAueipV7KgiX1vMgCxam1RNYiqE1w62n0/tJJnHtjW8ZHcQco5gY85jA3mi0L+nSA==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      '@sinclair/typebox': 0.27.8
    dev: true

  /@jridgewell/sourcemap-codec@1.4.15:
    resolution: {integrity: sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg==}
    dev: true

  /@napi-rs/cli@2.18.3:
    resolution: {integrity: sha512-L0f4kP0dyG8W5Qtc7MtP73VvLLrOLyRcUEBzknIfu8Jk4Jfhrsx1ItMHgyalYqMSslWdY3ojEfAaU5sx1VyeQQ==}
    engines: {node: '>= 10'}
    hasBin: true
    dev: true

  /@rollup/rollup-android-arm-eabi@4.7.0:
    resolution: {integrity: sha512-rGku10pL1StFlFvXX5pEv88KdGW6DHUghsxyP/aRYb9eH+74jTGJ3U0S/rtlsQ4yYq1Hcc7AMkoJOb1xu29Fxw==}
    cpu: [arm]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-android-arm64@4.7.0:
    resolution: {integrity: sha512-/EBw0cuJ/KVHiU2qyVYUhogXz7W2vXxBzeE9xtVIMC+RyitlY2vvaoysMUqASpkUtoNIHlnKTu/l7mXOPgnKOA==}
    cpu: [arm64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-darwin-arm64@4.7.0:
    resolution: {integrity: sha512-4VXG1bgvClJdbEYYjQ85RkOtwN8sqI3uCxH0HC5w9fKdqzRzgG39K7GAehATGS8jghA7zNoS5CjSKkDEqWmNZg==}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-darwin-x64@4.7.0:
    resolution: {integrity: sha512-/ImhO+T/RWJ96hUbxiCn2yWI0/MeQZV/aeukQQfhxiSXuZJfyqtdHPUPrc84jxCfXTxbJLmg4q+GBETeb61aNw==}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-arm-gnueabihf@4.7.0:
    resolution: {integrity: sha512-zhye8POvTyUXlKbfPBVqoHy3t43gIgffY+7qBFqFxNqVtltQLtWeHNAbrMnXiLIfYmxcoL/feuLDote2tx+Qbg==}
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-arm64-gnu@4.7.0:
    resolution: {integrity: sha512-RAdr3OJnUum6Vs83cQmKjxdTg31zJnLLTkjhcFt0auxM6jw00GD6IPFF42uasYPr/wGC6TRm7FsQiJyk0qIEfg==}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-arm64-musl@4.7.0:
    resolution: {integrity: sha512-nhWwYsiJwZGq7SyR3afS3EekEOsEAlrNMpPC4ZDKn5ooYSEjDLe9W/xGvoIV8/F/+HNIY6jY8lIdXjjxfxopXw==}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-riscv64-gnu@4.7.0:
    resolution: {integrity: sha512-rlfy5RnQG1aop1BL/gjdH42M2geMUyVQqd52GJVirqYc787A/XVvl3kQ5NG/43KXgOgE9HXgCaEH05kzQ+hLoA==}
    cpu: [riscv64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-x64-gnu@4.7.0:
    resolution: {integrity: sha512-cCkoGlGWfBobdDtiiypxf79q6k3/iRVGu1HVLbD92gWV5WZbmuWJCgRM4x2N6i7ljGn1cGytPn9ZAfS8UwF6vg==}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-x64-musl@4.7.0:
    resolution: {integrity: sha512-R2oBf2p/Arc1m+tWmiWbpHBjEcJnHVnv6bsypu4tcKdrYTpDfl1UT9qTyfkIL1iiii5D4WHxUHCg5X0pzqmxFg==}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-win32-arm64-msvc@4.7.0:
    resolution: {integrity: sha512-CPtgaQL1aaPc80m8SCVEoxFGHxKYIt3zQYC3AccL/SqqiWXblo3pgToHuBwR8eCP2Toa+X1WmTR/QKFMykws7g==}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-win32-ia32-msvc@4.7.0:
    resolution: {integrity: sha512-pmioUlttNh9GXF5x2CzNa7Z8kmRTyhEzzAC+2WOOapjewMbl+3tGuAnxbwc5JyG8Jsz2+hf/QD/n5VjimOZ63g==}
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-win32-x64-msvc@4.7.0:
    resolution: {integrity: sha512-SeZzC2QhhdBQUm3U0c8+c/P6UlRyBcLL2Xp5KX7z46WXZxzR8RJSIWL9wSUeBTgxog5LTPJuPj0WOT9lvrtP7Q==}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@sinclair/typebox@0.27.8:
    resolution: {integrity: sha512-+Fj43pSMwJs4KRrH/938Uf+uAELIgVBmQzg/q1YG10djyfA3TnrU8N8XzqCh/okZdszqBQTZf96idMfE5lnwTA==}
    dev: true

  /@smithy/abort-controller@2.0.15:
    resolution: {integrity: sha512-JkS36PIS3/UCbq/MaozzV7jECeL+BTt4R75bwY8i+4RASys4xOyUS1HsRyUNSqUXFP4QyCz5aNnh3ltuaxv+pw==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@smithy/chunked-blob-reader-native@2.0.1:
    resolution: {integrity: sha512-N2oCZRglhWKm7iMBu7S6wDzXirjAofi7tAd26cxmgibRYOBS4D3hGfmkwCpHdASZzwZDD8rluh0Rcqw1JeZDRw==}
    dependencies:
      '@smithy/util-base64': 2.0.1
      tslib: 2.6.2
    dev: true

  /@smithy/chunked-blob-reader@2.0.0:
    resolution: {integrity: sha512-k+J4GHJsMSAIQPChGBrjEmGS+WbPonCXesoqP9fynIqjn7rdOThdH8FAeCmokP9mxTYKQAKoHCLPzNlm6gh7Wg==}
    dependencies:
      tslib: 2.6.2
    dev: true

  /@smithy/config-resolver@2.0.21:
    resolution: {integrity: sha512-rlLIGT+BeqjnA6C2FWumPRJS1UW07iU5ZxDHtFuyam4W65gIaOFMjkB90ofKCIh+0mLVQrQFrl/VLtQT/6FWTA==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@smithy/node-config-provider': 2.1.8
      '@smithy/types': 2.7.0
      '@smithy/util-config-provider': 2.0.0
      '@smithy/util-middleware': 2.0.8
      tslib: 2.6.2
    dev: true

  /@smithy/credential-provider-imds@2.1.4:
    resolution: {integrity: sha512-cwPJN1fa1YOQzhBlTXRavABEYRRchci1X79QRwzaNLySnIMJfztyv1Zkst0iZPLMnpn8+CnHu3wOHS11J5Dr3A==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@smithy/node-config-provider': 2.1.8
      '@smithy/property-provider': 2.0.16
      '@smithy/types': 2.7.0
      '@smithy/url-parser': 2.0.15
      tslib: 2.6.2
    dev: true

  /@smithy/eventstream-codec@2.0.15:
    resolution: {integrity: sha512-crjvz3j1gGPwA0us6cwS7+5gAn35CTmqu/oIxVbYJo2Qm/sGAye6zGJnMDk3BKhWZw5kcU1G4MxciTkuBpOZPg==}
    dependencies:
      '@aws-crypto/crc32': 3.0.0
      '@smithy/types': 2.7.0
      '@smithy/util-hex-encoding': 2.0.0
      tslib: 2.6.2
    dev: true

  /@smithy/eventstream-serde-browser@2.0.15:
    resolution: {integrity: sha512-WiFG5N9j3jmS5P0z5Xev6dO0c3lf7EJYC2Ncb0xDnWFvShwXNn741AF71ABr5EcZw8F4rQma0362MMjAwJeZog==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@smithy/eventstream-serde-universal': 2.0.15
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@smithy/eventstream-serde-config-resolver@2.0.15:
    resolution: {integrity: sha512-o65d2LRjgCbWYH+VVNlWXtmsI231SO99ZTOL4UuIPa6WTjbSHWtlXvUcJG9libhEKWmEV9DIUiH2IqyPWi7ubA==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@smithy/eventstream-serde-node@2.0.15:
    resolution: {integrity: sha512-9OOXiIhHq1VeOG6xdHkn2ZayfMYM3vzdUTV3zhcCnt+tMqA3BJK3XXTJFRR2BV28rtRM778DzqbBTf+hqwQPTg==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@smithy/eventstream-serde-universal': 2.0.15
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@smithy/eventstream-serde-universal@2.0.15:
    resolution: {integrity: sha512-dP8AQp/pXlWBjvL0TaPBJC3rM0GoYv7O0Uim8d/7UKZ2Wo13bFI3/BhQfY/1DeiP1m23iCHFNFtOQxfQNBB8rQ==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@smithy/eventstream-codec': 2.0.15
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@smithy/fetch-http-handler@2.3.1:
    resolution: {integrity: sha512-6MNk16fqb8EwcYY8O8WxB3ArFkLZ2XppsSNo1h7SQcFdDDwIumiJeO6wRzm7iB68xvsOQzsdQKbdtTieS3hfSQ==}
    dependencies:
      '@smithy/protocol-http': 3.0.11
      '@smithy/querystring-builder': 2.0.15
      '@smithy/types': 2.7.0
      '@smithy/util-base64': 2.0.1
      tslib: 2.6.2
    dev: true

  /@smithy/hash-blob-browser@2.0.16:
    resolution: {integrity: sha512-cSYRi05LA7DZDwjB1HL0BP8B56eUNNeLglVH147QTXFyuXJq/7erAIiLRfsyXB8+GfFHkSS5BHbc76a7k/AYPA==}
    dependencies:
      '@smithy/chunked-blob-reader': 2.0.0
      '@smithy/chunked-blob-reader-native': 2.0.1
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@smithy/hash-node@2.0.17:
    resolution: {integrity: sha512-Il6WuBcI1nD+e2DM7tTADMf01wEPGK8PAhz4D+YmDUVaoBqlA+CaH2uDJhiySifmuKBZj748IfygXty81znKhw==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@smithy/types': 2.7.0
      '@smithy/util-buffer-from': 2.0.0
      '@smithy/util-utf8': 2.0.2
      tslib: 2.6.2
    dev: true

  /@smithy/hash-stream-node@2.0.17:
    resolution: {integrity: sha512-ey8DtnATzp1mOXgS7rqMwSmAki6iJA+jgNucKcxRkhMB1rrICfHg+rhmIF50iLPDHUhTcS5pBMOrLzzpZftvNQ==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@smithy/types': 2.7.0
      '@smithy/util-utf8': 2.0.2
      tslib: 2.6.2
    dev: true

  /@smithy/invalid-dependency@2.0.15:
    resolution: {integrity: sha512-dlEKBFFwVfzA5QroHlBS94NpgYjXhwN/bFfun+7w3rgxNvVy79SK0w05iGc7UAeC5t+D7gBxrzdnD6hreZnDVQ==}
    dependencies:
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@smithy/is-array-buffer@2.0.0:
    resolution: {integrity: sha512-z3PjFjMyZNI98JFRJi/U0nGoLWMSJlDjAW4QUX2WNZLas5C0CmVV6LJ01JI0k90l7FvpmixjWxPFmENSClQ7ug==}
    engines: {node: '>=14.0.0'}
    dependencies:
      tslib: 2.6.2
    dev: true

  /@smithy/md5-js@2.0.17:
    resolution: {integrity: sha512-jmISTCnEkOnm2oCNx/rMkvBT/eQh3aA6nktevkzbmn/VYqYEuc5Z2n5sTTqsciMSO01Lvf56wG1A4twDqovYeQ==}
    dependencies:
      '@smithy/types': 2.7.0
      '@smithy/util-utf8': 2.0.2
      tslib: 2.6.2
    dev: true

  /@smithy/middleware-content-length@2.0.17:
    resolution: {integrity: sha512-OyadvMcKC7lFXTNBa8/foEv7jOaqshQZkjWS9coEXPRZnNnihU/Ls+8ZuJwGNCOrN2WxXZFmDWhegbnM4vak8w==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@smithy/protocol-http': 3.0.11
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@smithy/middleware-endpoint@2.2.3:
    resolution: {integrity: sha512-nYfxuq0S/xoAjdLbyn1ixeVB6cyH9wYCMtbbOCpcCRYR5u2mMtqUtVjjPAZ/DIdlK3qe0tpB0Q76szFGNuz+kQ==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@smithy/middleware-serde': 2.0.15
      '@smithy/node-config-provider': 2.1.8
      '@smithy/shared-ini-file-loader': 2.2.7
      '@smithy/types': 2.7.0
      '@smithy/url-parser': 2.0.15
      '@smithy/util-middleware': 2.0.8
      tslib: 2.6.2
    dev: true

  /@smithy/middleware-retry@2.0.24:
    resolution: {integrity: sha512-q2SvHTYu96N7lYrn3VSuX3vRpxXHR/Cig6MJpGWxd0BWodUQUWlKvXpWQZA+lTaFJU7tUvpKhRd4p4MU3PbeJg==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@smithy/node-config-provider': 2.1.8
      '@smithy/protocol-http': 3.0.11
      '@smithy/service-error-classification': 2.0.8
      '@smithy/smithy-client': 2.1.18
      '@smithy/types': 2.7.0
      '@smithy/util-middleware': 2.0.8
      '@smithy/util-retry': 2.0.8
      tslib: 2.6.2
      uuid: 8.3.2
    dev: true

  /@smithy/middleware-serde@2.0.15:
    resolution: {integrity: sha512-FOZRFk/zN4AT4wzGuBY+39XWe+ZnCFd0gZtyw3f9Okn2CJPixl9GyWe98TIaljeZdqWkgrzGyPre20AcW2UMHQ==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@smithy/middleware-stack@2.0.9:
    resolution: {integrity: sha512-bCB5dUtGQ5wh7QNL2ELxmDc6g7ih7jWU3Kx6MYH1h4mZbv9xL3WyhKHojRltThCB1arLPyTUFDi+x6fB/oabtA==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@smithy/node-config-provider@2.1.8:
    resolution: {integrity: sha512-+w26OKakaBUGp+UG+dxYZtFb5fs3tgHg3/QrRrmUZj+rl3cIuw840vFUXX35cVPTUCQIiTqmz7CpVF7+hdINdQ==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@smithy/property-provider': 2.0.16
      '@smithy/shared-ini-file-loader': 2.2.7
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@smithy/node-http-handler@2.2.1:
    resolution: {integrity: sha512-8iAKQrC8+VFHPAT8pg4/j6hlsTQh+NKOWlctJBrYtQa4ExcxX7aSg3vdQ2XLoYwJotFUurg/NLqFCmZaPRrogw==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@smithy/abort-controller': 2.0.15
      '@smithy/protocol-http': 3.0.11
      '@smithy/querystring-builder': 2.0.15
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@smithy/property-provider@2.0.16:
    resolution: {integrity: sha512-28Ky0LlOqtEjwg5CdHmwwaDRHcTWfPRzkT6HrhwOSRS2RryAvuDfJrZpM+BMcrdeCyEg1mbcgIMoqTla+rdL8Q==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@smithy/protocol-http@3.0.11:
    resolution: {integrity: sha512-3ziB8fHuXIRamV/akp/sqiWmNPR6X+9SB8Xxnozzj+Nq7hSpyKdFHd1FLpBkgfGFUTzzcBJQlDZPSyxzmdcx5A==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@smithy/querystring-builder@2.0.15:
    resolution: {integrity: sha512-e1q85aT6HutvouOdN+dMsN0jcdshp50PSCvxDvo6aIM57LqeXimjfONUEgfqQ4IFpYWAtVixptyIRE5frMp/2A==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@smithy/types': 2.7.0
      '@smithy/util-uri-escape': 2.0.0
      tslib: 2.6.2
    dev: true

  /@smithy/querystring-parser@2.0.15:
    resolution: {integrity: sha512-jbBvoK3cc81Cj1c1TH1qMYxNQKHrYQ2DoTntN9FBbtUWcGhc+T4FP6kCKYwRLXyU4AajwGIZstvNAmIEgUUNTQ==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@smithy/service-error-classification@2.0.8:
    resolution: {integrity: sha512-jCw9+005im8tsfYvwwSc4TTvd29kXRFkH9peQBg5R/4DD03ieGm6v6Hpv9nIAh98GwgYg1KrztcINC1s4o7/hg==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@smithy/types': 2.7.0
    dev: true

  /@smithy/shared-ini-file-loader@2.2.7:
    resolution: {integrity: sha512-0Qt5CuiogIuvQIfK+be7oVHcPsayLgfLJGkPlbgdbl0lD28nUKu4p11L+UG3SAEsqc9UsazO+nErPXw7+IgDpQ==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@smithy/signature-v4@2.0.17:
    resolution: {integrity: sha512-ru5IUbHUAYgJ5ZqZaBi6PEsMjFT/do0Eu21Qt7b07NuRuPlwAMhlqNRDy/KE9QAF20ygehb+xe9ebmyZ26/BSA==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@smithy/eventstream-codec': 2.0.15
      '@smithy/is-array-buffer': 2.0.0
      '@smithy/types': 2.7.0
      '@smithy/util-hex-encoding': 2.0.0
      '@smithy/util-middleware': 2.0.8
      '@smithy/util-uri-escape': 2.0.0
      '@smithy/util-utf8': 2.0.2
      tslib: 2.6.2
    dev: true

  /@smithy/smithy-client@2.1.18:
    resolution: {integrity: sha512-7FqdbaJiVaHJDD9IfDhmzhSDbpjyx+ZsfdYuOpDJF09rl8qlIAIlZNoSaflKrQ3cEXZN2YxGPaNWGhbYimyIRQ==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@smithy/middleware-stack': 2.0.9
      '@smithy/types': 2.7.0
      '@smithy/util-stream': 2.0.23
      tslib: 2.6.2
    dev: true

  /@smithy/types@2.7.0:
    resolution: {integrity: sha512-1OIFyhK+vOkMbu4aN2HZz/MomREkrAC/HqY5mlJMUJfGrPRwijJDTeiN8Rnj9zUaB8ogXAfIOtZrrgqZ4w7Wnw==}
    engines: {node: '>=14.0.0'}
    dependencies:
      tslib: 2.6.2
    dev: true

  /@smithy/url-parser@2.0.15:
    resolution: {integrity: sha512-sADUncUj9rNbOTrdDGm4EXlUs0eQ9dyEo+V74PJoULY4jSQxS+9gwEgsPYyiu8PUOv16JC/MpHonOgqP/IEDZA==}
    dependencies:
      '@smithy/querystring-parser': 2.0.15
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@smithy/util-base64@2.0.1:
    resolution: {integrity: sha512-DlI6XFYDMsIVN+GH9JtcRp3j02JEVuWIn/QOZisVzpIAprdsxGveFed0bjbMRCqmIFe8uetn5rxzNrBtIGrPIQ==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@smithy/util-buffer-from': 2.0.0
      tslib: 2.6.2
    dev: true

  /@smithy/util-body-length-browser@2.0.1:
    resolution: {integrity: sha512-NXYp3ttgUlwkaug4bjBzJ5+yIbUbUx8VsSLuHZROQpoik+gRkIBeEG9MPVYfvPNpuXb/puqodeeUXcKFe7BLOQ==}
    dependencies:
      tslib: 2.6.2
    dev: true

  /@smithy/util-body-length-node@2.1.0:
    resolution: {integrity: sha512-/li0/kj/y3fQ3vyzn36NTLGmUwAICb7Jbe/CsWCktW363gh1MOcpEcSO3mJ344Gv2dqz8YJCLQpb6hju/0qOWw==}
    engines: {node: '>=14.0.0'}
    dependencies:
      tslib: 2.6.2
    dev: true

  /@smithy/util-buffer-from@2.0.0:
    resolution: {integrity: sha512-/YNnLoHsR+4W4Vf2wL5lGv0ksg8Bmk3GEGxn2vEQt52AQaPSCuaO5PM5VM7lP1K9qHRKHwrPGktqVoAHKWHxzw==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@smithy/is-array-buffer': 2.0.0
      tslib: 2.6.2
    dev: true

  /@smithy/util-config-provider@2.0.0:
    resolution: {integrity: sha512-xCQ6UapcIWKxXHEU4Mcs2s7LcFQRiU3XEluM2WcCjjBtQkUN71Tb+ydGmJFPxMUrW/GWMgQEEGipLym4XG0jZg==}
    engines: {node: '>=14.0.0'}
    dependencies:
      tslib: 2.6.2
    dev: true

  /@smithy/util-defaults-mode-browser@2.0.22:
    resolution: {integrity: sha512-qcF20IHHH96FlktvBRICDXDhLPtpVmtksHmqNGtotb9B0DYWXsC6jWXrkhrrwF7tH26nj+npVTqh9isiFV1gdA==}
    engines: {node: '>= 10.0.0'}
    dependencies:
      '@smithy/property-provider': 2.0.16
      '@smithy/smithy-client': 2.1.18
      '@smithy/types': 2.7.0
      bowser: 2.11.0
      tslib: 2.6.2
    dev: true

  /@smithy/util-defaults-mode-node@2.0.29:
    resolution: {integrity: sha512-+uG/15VoUh6JV2fdY9CM++vnSuMQ1VKZ6BdnkUM7R++C/vLjnlg+ToiSR1FqKZbMmKBXmsr8c/TsDWMAYvxbxQ==}
    engines: {node: '>= 10.0.0'}
    dependencies:
      '@smithy/config-resolver': 2.0.21
      '@smithy/credential-provider-imds': 2.1.4
      '@smithy/node-config-provider': 2.1.8
      '@smithy/property-provider': 2.0.16
      '@smithy/smithy-client': 2.1.18
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@smithy/util-endpoints@1.0.7:
    resolution: {integrity: sha512-Q2gEind3jxoLk6hdKWyESMU7LnXz8aamVwM+VeVjOYzYT1PalGlY/ETa48hv2YpV4+YV604y93YngyzzzQ4IIA==}
    engines: {node: '>= 14.0.0'}
    dependencies:
      '@smithy/node-config-provider': 2.1.8
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@smithy/util-hex-encoding@2.0.0:
    resolution: {integrity: sha512-c5xY+NUnFqG6d7HFh1IFfrm3mGl29lC+vF+geHv4ToiuJCBmIfzx6IeHLg+OgRdPFKDXIw6pvi+p3CsscaMcMA==}
    engines: {node: '>=14.0.0'}
    dependencies:
      tslib: 2.6.2
    dev: true

  /@smithy/util-middleware@2.0.8:
    resolution: {integrity: sha512-qkvqQjM8fRGGA8P2ydWylMhenCDP8VlkPn8kiNuFEaFz9xnUKC2irfqsBSJrfrOB9Qt6pQsI58r3zvvumhFMkw==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@smithy/util-retry@2.0.8:
    resolution: {integrity: sha512-cQTPnVaVFMjjS6cb44WV2yXtHVyXDC5icKyIbejMarJEApYeJWpBU3LINTxHqp/tyLI+MZOUdosr2mZ3sdziNg==}
    engines: {node: '>= 14.0.0'}
    dependencies:
      '@smithy/service-error-classification': 2.0.8
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@smithy/util-stream@2.0.23:
    resolution: {integrity: sha512-OJMWq99LAZJUzUwTk+00plyxX3ESktBaGPhqNIEVab+53gLULiWN9B/8bRABLg0K6R6Xg4t80uRdhk3B/LZqMQ==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@smithy/fetch-http-handler': 2.3.1
      '@smithy/node-http-handler': 2.2.1
      '@smithy/types': 2.7.0
      '@smithy/util-base64': 2.0.1
      '@smithy/util-buffer-from': 2.0.0
      '@smithy/util-hex-encoding': 2.0.0
      '@smithy/util-utf8': 2.0.2
      tslib: 2.6.2
    dev: true

  /@smithy/util-uri-escape@2.0.0:
    resolution: {integrity: sha512-ebkxsqinSdEooQduuk9CbKcI+wheijxEb3utGXkCoYQkJnwTnLbH1JXGimJtUkQwNQbsbuYwG2+aFVyZf5TLaw==}
    engines: {node: '>=14.0.0'}
    dependencies:
      tslib: 2.6.2
    dev: true

  /@smithy/util-utf8@2.0.2:
    resolution: {integrity: sha512-qOiVORSPm6Ce4/Yu6hbSgNHABLP2VMv8QOC3tTDNHHlWY19pPyc++fBTbZPtx6egPXi4HQxKDnMxVxpbtX2GoA==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@smithy/util-buffer-from': 2.0.0
      tslib: 2.6.2
    dev: true

  /@smithy/util-waiter@2.0.15:
    resolution: {integrity: sha512-9Y+btzzB7MhLADW7xgD6SjvmoYaRkrb/9SCbNGmNdfO47v38rxb90IGXyDtAK0Shl9bMthTmLgjlfYc+vtz2Qw==}
    engines: {node: '>=14.0.0'}
    dependencies:
      '@smithy/abort-controller': 2.0.15
      '@smithy/types': 2.7.0
      tslib: 2.6.2
    dev: true

  /@swc-node/core@1.10.6(@swc/core@1.3.100):
    resolution: {integrity: sha512-lDIi/rPosmKIknWzvs2/Fi9zWRtbkx8OJ9pQaevhsoGzJSal8Pd315k1W5AIrnknfdAB4HqRN12fk6AhqnrEEw==}
    engines: {node: '>= 10'}
    peerDependencies:
      '@swc/core': '>= 1.3'
    dependencies:
      '@swc/core': 1.3.100
    dev: true

  /@swc-node/register@1.6.8(@swc/core@1.3.100)(typescript@5.3.3):
    resolution: {integrity: sha512-74ijy7J9CWr1Z88yO+ykXphV29giCrSpANQPQRooE0bObpkTO1g4RzQovIfbIaniBiGDDVsYwDoQ3FIrCE8HcQ==}
    peerDependencies:
      '@swc/core': '>= 1.3'
      typescript: '>= 4.3'
    dependencies:
      '@swc-node/core': 1.10.6(@swc/core@1.3.100)
      '@swc-node/sourcemap-support': 0.3.0
      '@swc/core': 1.3.100
      colorette: 2.0.20
      debug: 4.3.4
      pirates: 4.0.6
      tslib: 2.6.2
      typescript: 5.3.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@swc-node/sourcemap-support@0.3.0:
    resolution: {integrity: sha512-gqBJSmJMWomZFxlppaKea7NeAqFrDrrS0RMt24No92M3nJWcyI9YKGEQKl+EyJqZ5gh6w1s0cTklMHMzRwA1NA==}
    dependencies:
      source-map-support: 0.5.21
      tslib: 2.6.2
    dev: true

  /@swc/core-darwin-arm64@1.3.100:
    resolution: {integrity: sha512-XVWFsKe6ei+SsDbwmsuRkYck1SXRpO60Hioa4hoLwR8fxbA9eVp6enZtMxzVVMBi8ej5seZ4HZQeAWepbukiBw==}
    engines: {node: '>=10'}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@swc/core-darwin-x64@1.3.100:
    resolution: {integrity: sha512-KF/MXrnH1nakm1wbt4XV8FS7kvqD9TGmVxeJ0U4bbvxXMvzeYUurzg3AJUTXYmXDhH/VXOYJE5N5RkwZZPs5iA==}
    engines: {node: '>=10'}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@swc/core-linux-arm64-gnu@1.3.100:
    resolution: {integrity: sha512-p8hikNnAEJrw5vHCtKiFT4hdlQxk1V7vqPmvUDgL/qe2menQDK/i12tbz7/3BEQ4UqUPnvwpmVn2d19RdEMNxw==}
    engines: {node: '>=10'}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@swc/core-linux-arm64-musl@1.3.100:
    resolution: {integrity: sha512-BWx/0EeY89WC4q3AaIaBSGfQxkYxIlS3mX19dwy2FWJs/O+fMvF9oLk/CyJPOZzbp+1DjGeeoGFuDYpiNO91JA==}
    engines: {node: '>=10'}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@swc/core-linux-x64-gnu@1.3.100:
    resolution: {integrity: sha512-XUdGu3dxAkjsahLYnm8WijPfKebo+jHgHphDxaW0ovI6sTdmEGFDew7QzKZRlbYL2jRkUuuKuDGvD6lO5frmhA==}
    engines: {node: '>=10'}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@swc/core-linux-x64-musl@1.3.100:
    resolution: {integrity: sha512-PhoXKf+f0OaNW/GCuXjJ0/KfK9EJX7z2gko+7nVnEA0p3aaPtbP6cq1Ubbl6CMoPL+Ci3gZ7nYumDqXNc3CtLQ==}
    engines: {node: '>=10'}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@swc/core-win32-arm64-msvc@1.3.100:
    resolution: {integrity: sha512-PwLADZN6F9cXn4Jw52FeP/MCLVHm8vwouZZSOoOScDtihjY495SSjdPnlosMaRSR4wJQssGwiD/4MbpgQPqbAw==}
    engines: {node: '>=10'}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@swc/core-win32-ia32-msvc@1.3.100:
    resolution: {integrity: sha512-0f6nicKSLlDKlyPRl2JEmkpBV4aeDfRQg6n8mPqgL7bliZIcDahG0ej+HxgNjZfS3e0yjDxsNRa6sAqWU2Z60A==}
    engines: {node: '>=10'}
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@swc/core-win32-x64-msvc@1.3.100:
    resolution: {integrity: sha512-b7J0rPoMkRTa3XyUGt8PwCaIBuYWsL2DqbirrQKRESzgCvif5iNpqaM6kjIjI/5y5q1Ycv564CB51YDpiS8EtQ==}
    engines: {node: '>=10'}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@swc/core@1.3.100:
    resolution: {integrity: sha512-7dKgTyxJjlrMwFZYb1auj3Xq0D8ZBe+5oeIgfMlRU05doXZypYJe0LAk0yjj3WdbwYzpF+T1PLxwTWizI0pckw==}
    engines: {node: '>=10'}
    requiresBuild: true
    peerDependencies:
      '@swc/helpers': ^0.5.0
    peerDependenciesMeta:
      '@swc/helpers':
        optional: true
    dependencies:
      '@swc/counter': 0.1.2
      '@swc/types': 0.1.5
    optionalDependencies:
      '@swc/core-darwin-arm64': 1.3.100
      '@swc/core-darwin-x64': 1.3.100
      '@swc/core-linux-arm64-gnu': 1.3.100
      '@swc/core-linux-arm64-musl': 1.3.100
      '@swc/core-linux-x64-gnu': 1.3.100
      '@swc/core-linux-x64-musl': 1.3.100
      '@swc/core-win32-arm64-msvc': 1.3.100
      '@swc/core-win32-ia32-msvc': 1.3.100
      '@swc/core-win32-x64-msvc': 1.3.100
    dev: true

  /@swc/counter@0.1.2:
    resolution: {integrity: sha512-9F4ys4C74eSTEUNndnER3VJ15oru2NumfQxS8geE+f3eB5xvfxpWyqE5XlVnxb/R14uoXi6SLbBwwiDSkv+XEw==}
    dev: true

  /@swc/types@0.1.5:
    resolution: {integrity: sha512-myfUej5naTBWnqOCc/MdVOLVjXUXtIA+NpDrDBKJtLLg2shUjBu3cZmB/85RyitKc55+lUUyl7oRfLOvkr2hsw==}
    dev: true

  /@types/estree@1.0.6:
    resolution: {integrity: sha512-AYnb1nQyY49te+VRAVgmzfcgjYS91mY5P0TKUDCLEM+gNnA+3T6rWITXRLYCpahpqSQbN5cE+gHpnPyXjHWxcw==}
    dev: true

  /@types/node@18.19.3:
    resolution: {integrity: sha512-k5fggr14DwAytoA/t8rPrIz++lXK7/DqckthCmoZOKNsEbJkId4Z//BqgApXBUGrGddrigYa1oqheo/7YmW4rg==}
    dependencies:
      undici-types: 5.26.5
    dev: true

  /@types/prop-types@15.7.11:
    resolution: {integrity: sha512-ga8y9v9uyeiLdpKddhxYQkxNDrfvuPrlFb0N1qnZZByvcElJaXthF1UhvCh9TLWJBEHeNtdnbysW7Y6Uq8CVng==}
    dev: true

  /@types/react@18.2.48:
    resolution: {integrity: sha512-qboRCl6Ie70DQQG9hhNREz81jqC1cs9EVNcjQ1AU+jH6NFfSAhVVbrrY/+nSF+Bsk4AOwm9Qa61InvMCyV+H3w==}
    dependencies:
      '@types/prop-types': 15.7.11
      '@types/scheduler': 0.16.8
      csstype: 3.1.3
    dev: true

  /@types/scheduler@0.16.8:
    resolution: {integrity: sha512-WZLiwShhwLRmeV6zH+GkbOFT6Z6VklCItrDioxUnv+u4Ll+8vKeFySoFyK/0ctcRpOmwAicELfmys1sDc/Rw+A==}
    dev: true

  /@vitest/expect@1.6.0:
    resolution: {integrity: sha512-ixEvFVQjycy/oNgHjqsL6AZCDduC+tflRluaHIzKIsdbzkLn2U/iBnVeJwB6HsIjQBdfMR8Z0tRxKUsvFJEeWQ==}
    dependencies:
      '@vitest/spy': 1.6.0
      '@vitest/utils': 1.6.0
      chai: 4.3.10
    dev: true

  /@vitest/runner@1.6.0:
    resolution: {integrity: sha512-P4xgwPjwesuBiHisAVz/LSSZtDjOTPYZVmNAnpHHSR6ONrf8eCJOFRvUwdHn30F5M1fxhqtl7QZQUk2dprIXAg==}
    dependencies:
      '@vitest/utils': 1.6.0
      p-limit: 5.0.0
      pathe: 1.1.1
    dev: true

  /@vitest/snapshot@1.6.0:
    resolution: {integrity: sha512-+Hx43f8Chus+DCmygqqfetcAZrDJwvTj0ymqjQq4CvmpKFSTVteEOBzCusu1x2tt4OJcvBflyHUE0DZSLgEMtQ==}
    dependencies:
      magic-string: 0.30.5
      pathe: 1.1.1
      pretty-format: 29.7.0
    dev: true

  /@vitest/spy@1.6.0:
    resolution: {integrity: sha512-leUTap6B/cqi/bQkXUu6bQV5TZPx7pmMBKBQiI0rJA8c3pB56ZsaTbREnF7CJfmvAS4V2cXIBAh/3rVwrrCYgw==}
    dependencies:
      tinyspy: 2.2.0
    dev: true

  /@vitest/utils@1.6.0:
    resolution: {integrity: sha512-21cPiuGMoMZwiOHa2i4LXkMkMkCGzA+MVFV70jRwHo95dL4x/ts5GZhML1QWuy7yfp3WzK3lRvZi3JnXTYqrBw==}
    dependencies:
      diff-sequences: 29.6.3
      estree-walker: 3.0.3
      loupe: 2.3.7
      pretty-format: 29.7.0
    dev: true

  /acorn-walk@8.3.4:
    resolution: {integrity: sha512-ueEepnujpqee2o5aIYnvHU6C0A42MNdsIDeqy5BydrkuC5R1ZuUFnm27EeFJGoEHJQgn3uleRvmTXaJgfXbt4g==}
    engines: {node: '>=0.4.0'}
    dependencies:
      acorn: 8.11.2
    dev: true

  /acorn@8.11.2:
    resolution: {integrity: sha512-nc0Axzp/0FILLEVsm4fNwLCwMttvhEI263QtVPQcbpfZZ3ts0hLsZGOpE6czNlid7CJ9MlyH8reXkpsf3YUY4w==}
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: true

  /ansi-escapes@4.3.2:
    resolution: {integrity: sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==}
    engines: {node: '>=8'}
    dependencies:
      type-fest: 0.21.3
    dev: true

  /ansi-regex@5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==}
    engines: {node: '>=8'}
    dev: true

  /ansi-sequence-parser@1.1.1:
    resolution: {integrity: sha512-vJXt3yiaUL4UU546s3rPXlsry/RnM730G1+HkpKE012AN0sx1eOrxSu95oKDIonskeLTijMgqWZ3uDEe3NFvyg==}
    dev: true

  /ansi-styles@4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}
    dependencies:
      color-convert: 2.0.1
    dev: true

  /ansi-styles@5.2.0:
    resolution: {integrity: sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA==}
    engines: {node: '>=10'}
    dev: true

  /assertion-error@1.1.0:
    resolution: {integrity: sha512-jgsaNduz+ndvGyFt3uSuWqvy4lCnIJiovtouQN5JZHOKCS2QuhEdbcQHFhVksz2N2U9hXJo8odG7ETyWlEeuDw==}
    dev: true

  /astral-regex@2.0.0:
    resolution: {integrity: sha512-Z7tMw1ytTXt5jqMcOP+OQteU1VuNK9Y02uuJtKQ1Sv69jXQKKg5cibLwGJow8yzZP+eAc18EmLGPal0bp36rvQ==}
    engines: {node: '>=8'}
    dev: true

  /balanced-match@1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}
    dev: true

  /benchmark@2.1.4:
    resolution: {integrity: sha512-l9MlfN4M1K/H2fbhfMy3B7vJd6AGKJVQn2h6Sg/Yx+KckoUA7ewS5Vv6TjSq18ooE1kS9hhAlQRH3AkXIh/aOQ==}
    dependencies:
      lodash: 4.17.21
      platform: 1.3.6
    dev: true

  /benny@3.7.1:
    resolution: {integrity: sha512-USzYxODdVfOS7JuQq/L0naxB788dWCiUgUTxvN+WLPt/JfcDURNNj8kN/N+uK6PDvuR67/9/55cVKGPleFQINA==}
    engines: {node: '>=12'}
    dependencies:
      '@arrows/composition': 1.2.2
      '@arrows/dispatch': 1.0.3
      '@arrows/multimethod': 1.4.1
      benchmark: 2.1.4
      common-tags: 1.8.2
      fs-extra: 10.1.0
      json2csv: 5.0.7
      kleur: 4.1.5
      log-update: 4.0.0
    dev: true

  /bowser@2.11.0:
    resolution: {integrity: sha512-AlcaJBi/pqqJBIQ8U9Mcpc9i8Aqxn88Skv5d+xBX006BY5u8N3mGLHa5Lgppa7L/HfwgwLgZ6NYs+Ag6uUmJRA==}
    dev: true

  /brace-expansion@2.0.1:
    resolution: {integrity: sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==}
    dependencies:
      balanced-match: 1.0.2
    dev: true

  /buffer-from@1.1.2:
    resolution: {integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==}
    dev: true

  /cac@6.7.14:
    resolution: {integrity: sha512-b6Ilus+c3RrdDk+JhLKUAQfzzgLEPy6wcXqS7f/xe1EETvsDP6GORG7SFuOs6cID5YkqchW/LXZbX5bc8j7ZcQ==}
    engines: {node: '>=8'}
    dev: true

  /chai@4.3.10:
    resolution: {integrity: sha512-0UXG04VuVbruMUYbJ6JctvH0YnC/4q3/AkT18q4NaITo91CUm0liMS9VqzT9vZhVQ/1eqPanMWjBM+Juhfb/9g==}
    engines: {node: '>=4'}
    dependencies:
      assertion-error: 1.1.0
      check-error: 1.0.3
      deep-eql: 4.1.3
      get-func-name: 2.0.2
      loupe: 2.3.7
      pathval: 1.1.1
      type-detect: 4.0.8
    dev: true

  /check-error@1.0.3:
    resolution: {integrity: sha512-iKEoDYaRmd1mxM90a2OEfWhjsjPpYPuQ+lMYsoxB126+t8fw7ySEO48nmDg5COTjxDI65/Y2OWpeEHk3ZOe8zg==}
    dependencies:
      get-func-name: 2.0.2
    dev: true

  /cli-cursor@3.1.0:
    resolution: {integrity: sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==}
    engines: {node: '>=8'}
    dependencies:
      restore-cursor: 3.1.0
    dev: true

  /color-convert@2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}
    dependencies:
      color-name: 1.1.4
    dev: true

  /color-name@1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}
    dev: true

  /colorette@2.0.20:
    resolution: {integrity: sha512-IfEDxwoWIjkeXL1eXcDiow4UbKjhLdq6/EuSVR9GMN7KVH3r9gQ83e73hsz1Nd1T3ijd5xv1wcWRYO+D6kCI2w==}
    dev: true

  /commander@6.2.1:
    resolution: {integrity: sha512-U7VdrJFnJgo4xjrHpTzu0yrHPGImdsmD95ZlgYSEajAn2JKzDhDTPG9kBTefmObL2w/ngeZnilk+OV9CG3d7UA==}
    engines: {node: '>= 6'}
    dev: true

  /common-tags@1.8.2:
    resolution: {integrity: sha512-gk/Z852D2Wtb//0I+kRFNKKE9dIIVirjoqPoA1wJU+XePVXZfGeBpk45+A1rKO4Q43prqWBNY/MiIeRLbPWUaA==}
    engines: {node: '>=4.0.0'}
    dev: true

  /cross-spawn@7.0.3:
    resolution: {integrity: sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==}
    engines: {node: '>= 8'}
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2
    dev: true

  /csstype@3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==}
    dev: true

  /debug@4.3.4:
    resolution: {integrity: sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.2
    dev: true

  /deep-eql@4.1.3:
    resolution: {integrity: sha512-WaEtAOpRA1MQ0eohqZjpGD8zdI0Ovsm8mmFhaDN8dvDZzyoUMcYDnf5Y6iu7HTXxf8JDS23qWa4a+hKCDyOPzw==}
    engines: {node: '>=6'}
    dependencies:
      type-detect: 4.0.8
    dev: true

  /diff-sequences@29.6.3:
    resolution: {integrity: sha512-EjePK1srD3P08o2j4f0ExnylqRs5B9tJjcp9t1krH2qRi8CCdsYfwe9JgSLurFBWwq4uOlipzfk5fHNvwFKr8Q==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dev: true

  /dotenv@16.3.1:
    resolution: {integrity: sha512-IPzF4w4/Rd94bA9imS68tZBaYyBWSCE47V1RGuMrB94iyTOIEwRmVL2x/4An+6mETpLrKJ5hQkB8W4kFAadeIQ==}
    engines: {node: '>=12'}
    dev: true

  /emoji-regex@8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}
    dev: true

  /esbuild@0.19.8:
    resolution: {integrity: sha512-l7iffQpT2OrZfH2rXIp7/FkmaeZM0vxbxN9KfiCwGYuZqzMg/JdvX26R31Zxn/Pxvsrg3Y9N6XTcnknqDyyv4w==}
    engines: {node: '>=12'}
    hasBin: true
    requiresBuild: true
    optionalDependencies:
      '@esbuild/android-arm': 0.19.8
      '@esbuild/android-arm64': 0.19.8
      '@esbuild/android-x64': 0.19.8
      '@esbuild/darwin-arm64': 0.19.8
      '@esbuild/darwin-x64': 0.19.8
      '@esbuild/freebsd-arm64': 0.19.8
      '@esbuild/freebsd-x64': 0.19.8
      '@esbuild/linux-arm': 0.19.8
      '@esbuild/linux-arm64': 0.19.8
      '@esbuild/linux-ia32': 0.19.8
      '@esbuild/linux-loong64': 0.19.8
      '@esbuild/linux-mips64el': 0.19.8
      '@esbuild/linux-ppc64': 0.19.8
      '@esbuild/linux-riscv64': 0.19.8
      '@esbuild/linux-s390x': 0.19.8
      '@esbuild/linux-x64': 0.19.8
      '@esbuild/netbsd-x64': 0.19.8
      '@esbuild/openbsd-x64': 0.19.8
      '@esbuild/sunos-x64': 0.19.8
      '@esbuild/win32-arm64': 0.19.8
      '@esbuild/win32-ia32': 0.19.8
      '@esbuild/win32-x64': 0.19.8
    dev: true

  /estree-walker@3.0.3:
    resolution: {integrity: sha512-7RUKfXgSMMkzt6ZuXmqapOurLGPPfgj6l9uRZ7lRGolvk0y2yocc35LdcxKC5PQZdn2DMqioAQ2NoWcrTKmm6g==}
    dependencies:
      '@types/estree': 1.0.6
    dev: true

  /execa@8.0.1:
    resolution: {integrity: sha512-VyhnebXciFV2DESc+p6B+y0LjSm0krU4OgJN44qFAhBY0TJ+1V61tYD2+wHusZ6F9n5K+vl8k0sTy7PEfV4qpg==}
    engines: {node: '>=16.17'}
    dependencies:
      cross-spawn: 7.0.3
      get-stream: 8.0.1
      human-signals: 5.0.0
      is-stream: 3.0.0
      merge-stream: 2.0.0
      npm-run-path: 5.3.0
      onetime: 6.0.0
      signal-exit: 4.1.0
      strip-final-newline: 3.0.0
    dev: true

  /fast-deep-equal@3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==}
    dev: true

  /fast-xml-parser@4.2.5:
    resolution: {integrity: sha512-B9/wizE4WngqQftFPmdaMYlXoJlJOYxGQOanC77fq9k8+Z0v5dDSVh+3glErdIROP//s/jgb7ZuxKfB8nVyo0g==}
    hasBin: true
    dependencies:
      strnum: 1.0.5
    dev: true

  /fs-extra@10.1.0:
    resolution: {integrity: sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==}
    engines: {node: '>=12'}
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 6.1.0
      universalify: 2.0.1
    dev: true

  /fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /get-func-name@2.0.2:
    resolution: {integrity: sha512-8vXOvuE167CtIc3OyItco7N/dpRtBbYOsPsXCz7X/PMnlGjYjSGuZJgM1Y7mmew7BKf9BqvLX2tnOVy1BBUsxQ==}
    dev: true

  /get-stream@8.0.1:
    resolution: {integrity: sha512-VaUJspBffn/LMCJVoMvSAdmscJyS1auj5Zulnn5UoYcY531UWmdwhRWkcGKnGU93m5HSXP9LP2usOryrBtQowA==}
    engines: {node: '>=16'}
    dev: true

  /graceful-fs@4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==}
    dev: true

  /human-signals@5.0.0:
    resolution: {integrity: sha512-AXcZb6vzzrFAUE61HnN4mpLqd/cSIwNQjtNWR0euPm6y0iqx3G4gOXaIDdtdDwZmhwe82LA6+zinmW4UBWVePQ==}
    engines: {node: '>=16.17.0'}
    dev: true

  /is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}
    dev: true

  /is-stream@3.0.0:
    resolution: {integrity: sha512-LnQR4bZ9IADDRSkvpqMGvt/tEJWclzklNgSw48V5EAaAeDd6qGvN8ei6k5p0tvxSR171VmGyHuTiAOfxAbr8kA==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    dev: true

  /isexe@2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==}
    dev: true

  /js-tokens@9.0.0:
    resolution: {integrity: sha512-WriZw1luRMlmV3LGJaR6QOJjWwgLUTf89OwT2lUOyjX2dJGBwgmIkbcz+7WFZjrZM635JOIR517++e/67CP9dQ==}
    dev: true

  /json2csv@5.0.7:
    resolution: {integrity: sha512-YRZbUnyaJZLZUJSRi2G/MqahCyRv9n/ds+4oIetjDF3jWQA7AG7iSeKTiZiCNqtMZM7HDyt0e/W6lEnoGEmMGA==}
    engines: {node: '>= 10', npm: '>= 6.13.0'}
    deprecated: Package no longer supported. Contact Support at https://www.npmjs.com/support for more info.
    hasBin: true
    dependencies:
      commander: 6.2.1
      jsonparse: 1.3.1
      lodash.get: 4.4.2
    dev: true

  /jsonc-parser@3.2.0:
    resolution: {integrity: sha512-gfFQZrcTc8CnKXp6Y4/CBT3fTc0OVuDofpre4aEeEpSBPV5X5v4+Vmx+8snU7RLPrNHPKSgLxGo9YuQzz20o+w==}
    dev: true

  /jsonfile@6.1.0:
    resolution: {integrity: sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==}
    dependencies:
      universalify: 2.0.1
    optionalDependencies:
      graceful-fs: 4.2.11
    dev: true

  /jsonparse@1.3.1:
    resolution: {integrity: sha512-POQXvpdL69+CluYsillJ7SUhKvytYjW9vG/GKpnf+xP8UWgYEM/RaMzHHofbALDiKbbP1W8UEYmgGl39WkPZsg==}
    engines: {'0': node >= 0.2.0}
    dev: true

  /kleur@4.1.5:
    resolution: {integrity: sha512-o+NO+8WrRiQEE4/7nwRJhN1HWpVmJm511pBHUxPLtp0BUISzlBplORYSmTclCnJvQq2tKu/sgl3xVpkc7ZWuQQ==}
    engines: {node: '>=6'}
    dev: true

  /local-pkg@0.5.0:
    resolution: {integrity: sha512-ok6z3qlYyCDS4ZEU27HaU6x/xZa9Whf8jD4ptH5UZTQYZVYeb9bnZ3ojVhiJNLiXK1Hfc0GNbLXcmZ5plLDDBg==}
    engines: {node: '>=14'}
    dependencies:
      mlly: 1.4.2
      pkg-types: 1.0.3
    dev: true

  /lodash.get@4.4.2:
    resolution: {integrity: sha512-z+Uw/vLuy6gQe8cfaFWD7p0wVv8fJl3mbzXh33RS+0oW2wvUqiRXiQ69gLWSLpgB5/6sU+r6BlQR0MBILadqTQ==}
    dev: true

  /lodash@4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}
    dev: true

  /log-update@4.0.0:
    resolution: {integrity: sha512-9fkkDevMefjg0mmzWFBW8YkFP91OrizzkW3diF7CpG+S2EYdy4+TVfGwz1zeF8x7hCx1ovSPTOE9Ngib74qqUg==}
    engines: {node: '>=10'}
    dependencies:
      ansi-escapes: 4.3.2
      cli-cursor: 3.1.0
      slice-ansi: 4.0.0
      wrap-ansi: 6.2.0
    dev: true

  /loupe@2.3.7:
    resolution: {integrity: sha512-zSMINGVYkdpYSOBmLi0D1Uo7JU9nVdQKrHxC8eYlV+9YKK9WePqAlL7lSlorG/U2Fw1w0hTBmaa/jrQ3UbPHtA==}
    dependencies:
      get-func-name: 2.0.2
    dev: true

  /lunr@2.3.9:
    resolution: {integrity: sha512-zTU3DaZaF3Rt9rhN3uBMGQD3dD2/vFQqnvZCDv4dl5iOzq2IZQqTxu90r4E5J+nP70J3ilqVCrbho2eWaeW8Ow==}
    dev: true

  /magic-string@0.30.5:
    resolution: {integrity: sha512-7xlpfBaQaP/T6Vh8MO/EqXSW5En6INHEvEXQiuff7Gku0PWjU3uf6w/j9o7O+SpB5fOAkrI5HeoNgwjEO0pFsA==}
    engines: {node: '>=12'}
    dependencies:
      '@jridgewell/sourcemap-codec': 1.4.15
    dev: true

  /marked@4.3.0:
    resolution: {integrity: sha512-PRsaiG84bK+AMvxziE/lCFss8juXjNaWzVbN5tXAm4XjeaS9NAHhop+PjQxz2A9h8Q4M/xGmzP8vqNwy6JeK0A==}
    engines: {node: '>= 12'}
    hasBin: true
    dev: true

  /merge-stream@2.0.0:
    resolution: {integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==}
    dev: true

  /mimic-fn@2.1.0:
    resolution: {integrity: sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==}
    engines: {node: '>=6'}
    dev: true

  /mimic-fn@4.0.0:
    resolution: {integrity: sha512-vqiC06CuhBTUdZH+RYl8sFrL096vA45Ok5ISO6sE/Mr1jRbGH4Csnhi8f3wKVl7x8mO4Au7Ir9D3Oyv1VYMFJw==}
    engines: {node: '>=12'}
    dev: true

  /minimatch@9.0.3:
    resolution: {integrity: sha512-RHiac9mvaRw0x3AYRgDC1CxAP7HTcNrrECeA8YYJeWnpo+2Q5CegtZjaotWTWxDG3UeGA1coE05iH1mPjT/2mg==}
    engines: {node: '>=16 || 14 >=14.17'}
    dependencies:
      brace-expansion: 2.0.1
    dev: true

  /mlly@1.4.2:
    resolution: {integrity: sha512-i/Ykufi2t1EZ6NaPLdfnZk2AX8cs0d+mTzVKuPfqPKPatxLApaBoxJQ9x1/uckXtrS/U5oisPMDkNs0yQTaBRg==}
    dependencies:
      acorn: 8.11.2
      pathe: 1.1.1
      pkg-types: 1.0.3
      ufo: 1.3.2
    dev: true

  /ms@2.1.2:
    resolution: {integrity: sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==}
    dev: true

  /nanoid@3.3.7:
    resolution: {integrity: sha512-eSRppjcPIatRIMC1U6UngP8XFcz8MQWGQdt1MTBQ7NaAmvXDfvNxbvWV3x2y6CdEUciCSsDHDQZbhYaB8QEo2g==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true
    dev: true

  /npm-run-path@5.3.0:
    resolution: {integrity: sha512-ppwTtiJZq0O/ai0z7yfudtBpWIoxM8yE6nHi1X47eFR2EWORqfbu6CnPlNsjeN683eT0qG6H/Pyf9fCcvjnnnQ==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    dependencies:
      path-key: 4.0.0
    dev: true

  /onetime@5.1.2:
    resolution: {integrity: sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==}
    engines: {node: '>=6'}
    dependencies:
      mimic-fn: 2.1.0
    dev: true

  /onetime@6.0.0:
    resolution: {integrity: sha512-1FlR+gjXK7X+AsAHso35MnyN5KqGwJRi/31ft6x0M194ht7S+rWAvd7PHss9xSKMzE0asv1pyIHaJYq+BbacAQ==}
    engines: {node: '>=12'}
    dependencies:
      mimic-fn: 4.0.0
    dev: true

  /p-limit@5.0.0:
    resolution: {integrity: sha512-/Eaoq+QyLSiXQ4lyYV23f14mZRQcXnxfHrN0vCai+ak9G0pp9iEQukIIZq5NccEvwRB8PUnZT0KsOoDCINS1qQ==}
    engines: {node: '>=18'}
    dependencies:
      yocto-queue: 1.0.0
    dev: true

  /path-key@3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}
    dev: true

  /path-key@4.0.0:
    resolution: {integrity: sha512-haREypq7xkM7ErfgIyA0z+Bj4AGKlMSdlQE2jvJo6huWD1EdkKYV+G/T4nq0YEF2vgTT8kqMFKo1uHn950r4SQ==}
    engines: {node: '>=12'}
    dev: true

  /pathe@1.1.1:
    resolution: {integrity: sha512-d+RQGp0MAYTIaDBIMmOfMwz3E+LOZnxx1HZd5R18mmCZY0QBlK0LDZfPc8FW8Ed2DlvsuE6PRjroDY+wg4+j/Q==}
    dev: true

  /pathval@1.1.1:
    resolution: {integrity: sha512-Dp6zGqpTdETdR63lehJYPeIOqpiNBNtc7BpWSLrOje7UaIsE5aY92r/AunQA7rsXvet3lrJ3JnZX29UPTKXyKQ==}
    dev: true

  /picocolors@1.0.0:
    resolution: {integrity: sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ==}
    dev: true

  /pirates@4.0.6:
    resolution: {integrity: sha512-saLsH7WeYYPiD25LDuLRRY/i+6HaPYr6G1OUlN39otzkSTxKnubR9RTxS3/Kk50s1g2JTgFwWQDQyplC5/SHZg==}
    engines: {node: '>= 6'}
    dev: true

  /pkg-types@1.0.3:
    resolution: {integrity: sha512-nN7pYi0AQqJnoLPC9eHFQ8AcyaixBUOwvqc5TDnIKCMEE6I0y8P7OKA7fPexsXGCGxQDl/cmrLAp26LhcwxZ4A==}
    dependencies:
      jsonc-parser: 3.2.0
      mlly: 1.4.2
      pathe: 1.1.1
    dev: true

  /platform@1.3.6:
    resolution: {integrity: sha512-fnWVljUchTro6RiCFvCXBbNhJc2NijN7oIQxbwsyL0buWJPG85v81ehlHI9fXrJsMNgTofEoWIQeClKpgxFLrg==}
    dev: true

  /postcss@8.4.32:
    resolution: {integrity: sha512-D/kj5JNu6oo2EIy+XL/26JEDTlIbB8hw85G8StOE6L74RQAVVP5rej6wxCNqyMbR4RkPfqvezVbPw81Ngd6Kcw==}
    engines: {node: ^10 || ^12 || >=14}
    dependencies:
      nanoid: 3.3.7
      picocolors: 1.0.0
      source-map-js: 1.0.2
    dev: true

  /prettier@2.8.8:
    resolution: {integrity: sha512-tdN8qQGvNjw4CHbY+XXk0JgCXn9QiF21a55rBe5LJAU+kDyC4WQn4+awm2Xfk2lQMk5fKup9XgzTZtGkjBdP9Q==}
    engines: {node: '>=10.13.0'}
    hasBin: true
    dev: true

  /pretty-format@29.7.0:
    resolution: {integrity: sha512-Pdlw/oPxN+aXdmM9R00JVC9WVFoCLTKJvDVLgmJ+qAffBMxsV85l/Lu7sNx4zSzPyoL2euImuEwHhOXdEgNFZQ==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      '@jest/schemas': 29.6.3
      ansi-styles: 5.2.0
      react-is: 18.2.0
    dev: true

  /react-is@18.2.0:
    resolution: {integrity: sha512-xWGDIW6x921xtzPkhiULtthJHoJvBbF3q26fzloPCK0hsvxtPVelvftw3zjbHWSkR2km9Z+4uxbDDK/6Zw9B8w==}
    dev: true

  /restore-cursor@3.1.0:
    resolution: {integrity: sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA==}
    engines: {node: '>=8'}
    dependencies:
      onetime: 5.1.2
      signal-exit: 3.0.7
    dev: true

  /rollup@4.7.0:
    resolution: {integrity: sha512-7Kw0dUP4BWH78zaZCqF1rPyQ8D5DSU6URG45v1dqS/faNsx9WXyess00uTOZxKr7oR/4TOjO1CPudT8L1UsEgw==}
    engines: {node: '>=18.0.0', npm: '>=8.0.0'}
    hasBin: true
    optionalDependencies:
      '@rollup/rollup-android-arm-eabi': 4.7.0
      '@rollup/rollup-android-arm64': 4.7.0
      '@rollup/rollup-darwin-arm64': 4.7.0
      '@rollup/rollup-darwin-x64': 4.7.0
      '@rollup/rollup-linux-arm-gnueabihf': 4.7.0
      '@rollup/rollup-linux-arm64-gnu': 4.7.0
      '@rollup/rollup-linux-arm64-musl': 4.7.0
      '@rollup/rollup-linux-riscv64-gnu': 4.7.0
      '@rollup/rollup-linux-x64-gnu': 4.7.0
      '@rollup/rollup-linux-x64-musl': 4.7.0
      '@rollup/rollup-win32-arm64-msvc': 4.7.0
      '@rollup/rollup-win32-ia32-msvc': 4.7.0
      '@rollup/rollup-win32-x64-msvc': 4.7.0
      fsevents: 2.3.3
    dev: true

  /shebang-command@2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}
    dependencies:
      shebang-regex: 3.0.0
    dev: true

  /shebang-regex@3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}
    dev: true

  /shiki@0.14.6:
    resolution: {integrity: sha512-R4koBBlQP33cC8cpzX0hAoOURBHJILp4Aaduh2eYi+Vj8ZBqtK/5SWNEHBS3qwUMu8dqOtI/ftno3ESfNeVW9g==}
    dependencies:
      ansi-sequence-parser: 1.1.1
      jsonc-parser: 3.2.0
      vscode-oniguruma: 1.7.0
      vscode-textmate: 8.0.0
    dev: true

  /siginfo@2.0.0:
    resolution: {integrity: sha512-ybx0WO1/8bSBLEWXZvEd7gMW3Sn3JFlW3TvX1nREbDLRNQNaeNN8WK0meBwPdAaOI7TtRRRJn/Es1zhrrCHu7g==}
    dev: true

  /signal-exit@3.0.7:
    resolution: {integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==}
    dev: true

  /signal-exit@4.1.0:
    resolution: {integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==}
    engines: {node: '>=14'}
    dev: true

  /slice-ansi@4.0.0:
    resolution: {integrity: sha512-qMCMfhY040cVHT43K9BFygqYbUPFZKHOg7K73mtTWJRb8pyP3fzf4Ixd5SzdEJQ6MRUg/WBnOLxghZtKKurENQ==}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      astral-regex: 2.0.0
      is-fullwidth-code-point: 3.0.0
    dev: true

  /source-map-js@1.0.2:
    resolution: {integrity: sha512-R0XvVJ9WusLiqTCEiGCmICCMplcCkIwwR11mOSD9CR5u+IXYdiseeEuXCVAjS54zqwkLcPNnmU4OeJ6tUrWhDw==}
    engines: {node: '>=0.10.0'}
    dev: true

  /source-map-support@0.5.21:
    resolution: {integrity: sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==}
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1
    dev: true

  /source-map@0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}
    dev: true

  /stackback@0.0.2:
    resolution: {integrity: sha512-1XMJE5fQo1jGH6Y/7ebnwPOBEkIEnT4QF32d5R1+VXdXveM0IBMJt8zfaxX1P3QhVwrYe+576+jkANtSS2mBbw==}
    dev: true

  /std-env@3.6.0:
    resolution: {integrity: sha512-aFZ19IgVmhdB2uX599ve2kE6BIE3YMnQ6Gp6BURhW/oIzpXGKr878TQfAQZn1+i0Flcc/UKUy1gOlcfaUBCryg==}
    dev: true

  /string-width@4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==}
    engines: {node: '>=8'}
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1
    dev: true

  /strip-ansi@6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==}
    engines: {node: '>=8'}
    dependencies:
      ansi-regex: 5.0.1
    dev: true

  /strip-final-newline@3.0.0:
    resolution: {integrity: sha512-dOESqjYr96iWYylGObzd39EuNTa5VJxyvVAEm5Jnh7KGo75V43Hk1odPQkNDyXNmUR6k+gEiDVXnjB8HJ3crXw==}
    engines: {node: '>=12'}
    dev: true

  /strip-literal@2.1.0:
    resolution: {integrity: sha512-Op+UycaUt/8FbN/Z2TWPBLge3jWrP3xj10f3fnYxf052bKuS3EKs1ZQcVGjnEMdsNVAM+plXRdmjrZ/KgG3Skw==}
    dependencies:
      js-tokens: 9.0.0
    dev: true

  /strnum@1.0.5:
    resolution: {integrity: sha512-J8bbNyKKXl5qYcR36TIO8W3mVGVHrmmxsd5PAItGkmyzwJvybiw2IVq5nqd0i4LSNSkB/sx9VHllbfFdr9k1JA==}
    dev: true

  /tinybench@2.5.1:
    resolution: {integrity: sha512-65NKvSuAVDP/n4CqH+a9w2kTlLReS9vhsAP06MWx+/89nMinJyB2icyl58RIcqCmIggpojIGeuJGhjU1aGMBSg==}
    dev: true

  /tinypool@0.8.4:
    resolution: {integrity: sha512-i11VH5gS6IFeLY3gMBQ00/MmLncVP7JLXOw1vlgkytLmJK7QnEr7NXf0LBdxfmNPAeyetukOk0bOYrJrFGjYJQ==}
    engines: {node: '>=14.0.0'}
    dev: true

  /tinyspy@2.2.0:
    resolution: {integrity: sha512-d2eda04AN/cPOR89F7Xv5bK/jrQEhmcLFe6HFldoeO9AJtps+fqEnh486vnT/8y4bw38pSyxDcTCAq+Ks2aJTg==}
    engines: {node: '>=14.0.0'}
    dev: true

  /tslib@1.14.1:
    resolution: {integrity: sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==}
    dev: true

  /tslib@2.6.2:
    resolution: {integrity: sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q==}
    dev: true

  /type-detect@4.0.8:
    resolution: {integrity: sha512-0fr/mIH1dlO+x7TlcMy+bIDqKPsw/70tVyeHW787goQjhmqaZe10uwLujubK9q9Lg6Fiho1KUKDYz0Z7k7g5/g==}
    engines: {node: '>=4'}
    dev: true

  /type-fest@0.21.3:
    resolution: {integrity: sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==}
    engines: {node: '>=10'}
    dev: true

  /typedoc@0.25.4(typescript@5.3.3):
    resolution: {integrity: sha512-Du9ImmpBCw54bX275yJrxPVnjdIyJO/84co0/L9mwe0R3G4FSR6rQ09AlXVRvZEGMUg09+z/usc8mgygQ1aidA==}
    engines: {node: '>= 16'}
    hasBin: true
    peerDependencies:
      typescript: 4.6.x || 4.7.x || 4.8.x || 4.9.x || 5.0.x || 5.1.x || 5.2.x || 5.3.x
    dependencies:
      lunr: 2.3.9
      marked: 4.3.0
      minimatch: 9.0.3
      shiki: 0.14.6
      typescript: 5.3.3
    dev: true

  /typescript@5.3.3:
    resolution: {integrity: sha512-pXWcraxM0uxAS+tN0AG/BF2TyqmHO014Z070UsJ+pFvYuRSq8KH8DmWpnbXe0pEPDHXZV3FcAbJkijJ5oNEnWw==}
    engines: {node: '>=14.17'}
    hasBin: true
    dev: true

  /ufo@1.3.2:
    resolution: {integrity: sha512-o+ORpgGwaYQXgqGDwd+hkS4PuZ3QnmqMMxRuajK/a38L6fTpcE5GPIfrf+L/KemFzfUpeUQc1rRS1iDBozvnFA==}
    dev: true

  /undici-types@5.26.5:
    resolution: {integrity: sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==}
    dev: true

  /universalify@2.0.1:
    resolution: {integrity: sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==}
    engines: {node: '>= 10.0.0'}
    dev: true

  /uuid@8.3.2:
    resolution: {integrity: sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==}
    hasBin: true
    dev: true

  /vite-node@1.6.0(@types/node@18.19.3):
    resolution: {integrity: sha512-de6HJgzC+TFzOu0NTC4RAIsyf/DY/ibWDYQUcuEA84EMHhcefTUGkjFHKKEJhQN4A+6I0u++kr3l36ZF2d7XRw==}
    engines: {node: ^18.0.0 || >=20.0.0}
    hasBin: true
    dependencies:
      cac: 6.7.14
      debug: 4.3.4
      pathe: 1.1.1
      picocolors: 1.0.0
      vite: 5.0.6(@types/node@18.19.3)
    transitivePeerDependencies:
      - '@types/node'
      - less
      - lightningcss
      - sass
      - stylus
      - sugarss
      - supports-color
      - terser
    dev: true

  /vite@5.0.6(@types/node@18.19.3):
    resolution: {integrity: sha512-MD3joyAEBtV7QZPl2JVVUai6zHms3YOmLR+BpMzLlX2Yzjfcc4gTgNi09d/Rua3F4EtC8zdwPU8eQYyib4vVMQ==}
    engines: {node: ^18.0.0 || >=20.0.0}
    hasBin: true
    peerDependencies:
      '@types/node': ^18.0.0 || >=20.0.0
      less: '*'
      lightningcss: ^1.21.0
      sass: '*'
      stylus: '*'
      sugarss: '*'
      terser: ^5.4.0
    peerDependenciesMeta:
      '@types/node':
        optional: true
      less:
        optional: true
      lightningcss:
        optional: true
      sass:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true
    dependencies:
      '@types/node': 18.19.3
      esbuild: 0.19.8
      postcss: 8.4.32
      rollup: 4.7.0
    optionalDependencies:
      fsevents: 2.3.3
    dev: true

  /vitest@1.6.0(@types/node@18.19.3):
    resolution: {integrity: sha512-H5r/dN06swuFnzNFhq/dnz37bPXnq8xB2xB5JOVk8K09rUtoeNN+LHWkoQ0A/i3hvbUKKcCei9KpbxqHMLhLLA==}
    engines: {node: ^18.0.0 || >=20.0.0}
    hasBin: true
    peerDependencies:
      '@edge-runtime/vm': '*'
      '@types/node': ^18.0.0 || >=20.0.0
      '@vitest/browser': 1.6.0
      '@vitest/ui': 1.6.0
      happy-dom: '*'
      jsdom: '*'
    peerDependenciesMeta:
      '@edge-runtime/vm':
        optional: true
      '@types/node':
        optional: true
      '@vitest/browser':
        optional: true
      '@vitest/ui':
        optional: true
      happy-dom:
        optional: true
      jsdom:
        optional: true
    dependencies:
      '@types/node': 18.19.3
      '@vitest/expect': 1.6.0
      '@vitest/runner': 1.6.0
      '@vitest/snapshot': 1.6.0
      '@vitest/spy': 1.6.0
      '@vitest/utils': 1.6.0
      acorn-walk: 8.3.4
      chai: 4.3.10
      debug: 4.3.4
      execa: 8.0.1
      local-pkg: 0.5.0
      magic-string: 0.30.5
      pathe: 1.1.1
      picocolors: 1.0.0
      std-env: 3.6.0
      strip-literal: 2.1.0
      tinybench: 2.5.1
      tinypool: 0.8.4
      vite: 5.0.6(@types/node@18.19.3)
      vite-node: 1.6.0(@types/node@18.19.3)
      why-is-node-running: 2.2.2
    transitivePeerDependencies:
      - less
      - lightningcss
      - sass
      - stylus
      - sugarss
      - supports-color
      - terser
    dev: true

  /vscode-oniguruma@1.7.0:
    resolution: {integrity: sha512-L9WMGRfrjOhgHSdOYgCt/yRMsXzLDJSL7BPrOZt73gU0iWO4mpqzqQzOz5srxqTvMBaR0XZTSrVWo4j55Rc6cA==}
    dev: true

  /vscode-textmate@8.0.0:
    resolution: {integrity: sha512-AFbieoL7a5LMqcnOF04ji+rpXadgOXnZsxQr//r83kLPr7biP7am3g9zbaZIaBGwBRWeSvoMD4mgPdX3e4NWBg==}
    dev: true

  /which@2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true
    dependencies:
      isexe: 2.0.0
    dev: true

  /why-is-node-running@2.2.2:
    resolution: {integrity: sha512-6tSwToZxTOcotxHeA+qGCq1mVzKR3CwcJGmVcY+QE8SHy6TnpFnh8PAvPNHYr7EcuVeG0QSMxtYCuO1ta/G/oA==}
    engines: {node: '>=8'}
    hasBin: true
    dependencies:
      siginfo: 2.0.0
      stackback: 0.0.2
    dev: true

  /wrap-ansi@6.2.0:
    resolution: {integrity: sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA==}
    engines: {node: '>=8'}
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1
    dev: true

  /yocto-queue@1.0.0:
    resolution: {integrity: sha512-9bnSc/HEW2uRy67wc+T8UwauLuPJVn28jb+GtJY16iiKWyvmYJRXVT4UamsAEGQfPohgr2q4Tq0sQbQlxTfi1g==}
    engines: {node: '>=12.20'}
    dev: true
