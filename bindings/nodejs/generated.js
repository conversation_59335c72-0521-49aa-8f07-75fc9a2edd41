/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/* tslint:disable */
/* eslint-disable */
/* prettier-ignore */

/* auto-generated by NAPI-RS */

const { existsSync, readFileSync } = require('fs')
const { join } = require('path')

const { platform, arch } = process

let nativeBinding = null
let localFileExisted = false
let loadError = null

function isMusl() {
  // For Node 10
  if (!process.report || typeof process.report.getReport !== 'function') {
    try {
      const lddPath = require('child_process').execSync('which ldd').toString().trim()
      return readFileSync(lddPath, 'utf8').includes('musl')
    } catch (e) {
      return true
    }
  } else {
    const { glibcVersionRuntime } = process.report.getReport().header
    return !glibcVersionRuntime
  }
}

switch (platform) {
  case 'android':
    switch (arch) {
      case 'arm64':
        localFileExisted = existsSync(join(__dirname, 'opendal.android-arm64.node'))
        try {
          if (localFileExisted) {
            nativeBinding = require('./opendal.android-arm64.node')
          } else {
            nativeBinding = require('@opendal/lib-android-arm64')
          }
        } catch (e) {
          loadError = e
        }
        break
      case 'arm':
        localFileExisted = existsSync(join(__dirname, 'opendal.android-arm-eabi.node'))
        try {
          if (localFileExisted) {
            nativeBinding = require('./opendal.android-arm-eabi.node')
          } else {
            nativeBinding = require('@opendal/lib-android-arm-eabi')
          }
        } catch (e) {
          loadError = e
        }
        break
      default:
        throw new Error(`Unsupported architecture on Android ${arch}`)
    }
    break
  case 'win32':
    switch (arch) {
      case 'x64':
        localFileExisted = existsSync(
          join(__dirname, 'opendal.win32-x64-msvc.node')
        )
        try {
          if (localFileExisted) {
            nativeBinding = require('./opendal.win32-x64-msvc.node')
          } else {
            nativeBinding = require('@opendal/lib-win32-x64-msvc')
          }
        } catch (e) {
          loadError = e
        }
        break
      case 'ia32':
        localFileExisted = existsSync(
          join(__dirname, 'opendal.win32-ia32-msvc.node')
        )
        try {
          if (localFileExisted) {
            nativeBinding = require('./opendal.win32-ia32-msvc.node')
          } else {
            nativeBinding = require('@opendal/lib-win32-ia32-msvc')
          }
        } catch (e) {
          loadError = e
        }
        break
      case 'arm64':
        localFileExisted = existsSync(
          join(__dirname, 'opendal.win32-arm64-msvc.node')
        )
        try {
          if (localFileExisted) {
            nativeBinding = require('./opendal.win32-arm64-msvc.node')
          } else {
            nativeBinding = require('@opendal/lib-win32-arm64-msvc')
          }
        } catch (e) {
          loadError = e
        }
        break
      default:
        throw new Error(`Unsupported architecture on Windows: ${arch}`)
    }
    break
  case 'darwin':
    localFileExisted = existsSync(join(__dirname, 'opendal.darwin-universal.node'))
    try {
      if (localFileExisted) {
        nativeBinding = require('./opendal.darwin-universal.node')
      } else {
        nativeBinding = require('@opendal/lib-darwin-universal')
      }
      break
    } catch {}
    switch (arch) {
      case 'x64':
        localFileExisted = existsSync(join(__dirname, 'opendal.darwin-x64.node'))
        try {
          if (localFileExisted) {
            nativeBinding = require('./opendal.darwin-x64.node')
          } else {
            nativeBinding = require('@opendal/lib-darwin-x64')
          }
        } catch (e) {
          loadError = e
        }
        break
      case 'arm64':
        localFileExisted = existsSync(
          join(__dirname, 'opendal.darwin-arm64.node')
        )
        try {
          if (localFileExisted) {
            nativeBinding = require('./opendal.darwin-arm64.node')
          } else {
            nativeBinding = require('@opendal/lib-darwin-arm64')
          }
        } catch (e) {
          loadError = e
        }
        break
      default:
        throw new Error(`Unsupported architecture on macOS: ${arch}`)
    }
    break
  case 'freebsd':
    if (arch !== 'x64') {
      throw new Error(`Unsupported architecture on FreeBSD: ${arch}`)
    }
    localFileExisted = existsSync(join(__dirname, 'opendal.freebsd-x64.node'))
    try {
      if (localFileExisted) {
        nativeBinding = require('./opendal.freebsd-x64.node')
      } else {
        nativeBinding = require('@opendal/lib-freebsd-x64')
      }
    } catch (e) {
      loadError = e
    }
    break
  case 'linux':
    switch (arch) {
      case 'x64':
        if (isMusl()) {
          localFileExisted = existsSync(
            join(__dirname, 'opendal.linux-x64-musl.node')
          )
          try {
            if (localFileExisted) {
              nativeBinding = require('./opendal.linux-x64-musl.node')
            } else {
              nativeBinding = require('@opendal/lib-linux-x64-musl')
            }
          } catch (e) {
            loadError = e
          }
        } else {
          localFileExisted = existsSync(
            join(__dirname, 'opendal.linux-x64-gnu.node')
          )
          try {
            if (localFileExisted) {
              nativeBinding = require('./opendal.linux-x64-gnu.node')
            } else {
              nativeBinding = require('@opendal/lib-linux-x64-gnu')
            }
          } catch (e) {
            loadError = e
          }
        }
        break
      case 'arm64':
        if (isMusl()) {
          localFileExisted = existsSync(
            join(__dirname, 'opendal.linux-arm64-musl.node')
          )
          try {
            if (localFileExisted) {
              nativeBinding = require('./opendal.linux-arm64-musl.node')
            } else {
              nativeBinding = require('@opendal/lib-linux-arm64-musl')
            }
          } catch (e) {
            loadError = e
          }
        } else {
          localFileExisted = existsSync(
            join(__dirname, 'opendal.linux-arm64-gnu.node')
          )
          try {
            if (localFileExisted) {
              nativeBinding = require('./opendal.linux-arm64-gnu.node')
            } else {
              nativeBinding = require('@opendal/lib-linux-arm64-gnu')
            }
          } catch (e) {
            loadError = e
          }
        }
        break
      case 'arm':
        if (isMusl()) {
          localFileExisted = existsSync(
            join(__dirname, 'opendal.linux-arm-musleabihf.node')
          )
          try {
            if (localFileExisted) {
              nativeBinding = require('./opendal.linux-arm-musleabihf.node')
            } else {
              nativeBinding = require('@opendal/lib-linux-arm-musleabihf')
            }
          } catch (e) {
            loadError = e
          }
        } else {
          localFileExisted = existsSync(
            join(__dirname, 'opendal.linux-arm-gnueabihf.node')
          )
          try {
            if (localFileExisted) {
              nativeBinding = require('./opendal.linux-arm-gnueabihf.node')
            } else {
              nativeBinding = require('@opendal/lib-linux-arm-gnueabihf')
            }
          } catch (e) {
            loadError = e
          }
        }
        break
      case 'riscv64':
        if (isMusl()) {
          localFileExisted = existsSync(
            join(__dirname, 'opendal.linux-riscv64-musl.node')
          )
          try {
            if (localFileExisted) {
              nativeBinding = require('./opendal.linux-riscv64-musl.node')
            } else {
              nativeBinding = require('@opendal/lib-linux-riscv64-musl')
            }
          } catch (e) {
            loadError = e
          }
        } else {
          localFileExisted = existsSync(
            join(__dirname, 'opendal.linux-riscv64-gnu.node')
          )
          try {
            if (localFileExisted) {
              nativeBinding = require('./opendal.linux-riscv64-gnu.node')
            } else {
              nativeBinding = require('@opendal/lib-linux-riscv64-gnu')
            }
          } catch (e) {
            loadError = e
          }
        }
        break
      case 's390x':
        localFileExisted = existsSync(
          join(__dirname, 'opendal.linux-s390x-gnu.node')
        )
        try {
          if (localFileExisted) {
            nativeBinding = require('./opendal.linux-s390x-gnu.node')
          } else {
            nativeBinding = require('@opendal/lib-linux-s390x-gnu')
          }
        } catch (e) {
          loadError = e
        }
        break
      default:
        throw new Error(`Unsupported architecture on Linux: ${arch}`)
    }
    break
  default:
    throw new Error(`Unsupported OS: ${platform}, architecture: ${arch}`)
}

if (!nativeBinding) {
  if (loadError) {
    throw loadError
  }
  throw new Error(`Failed to load native binding`)
}

const { Capability, Operator, Entry, Metadata, BlockingReader, Reader, BlockingWriter, Writer, Lister, BlockingLister, Layer, RetryLayer } = nativeBinding

module.exports.Capability = Capability
module.exports.Operator = Operator
module.exports.Entry = Entry
module.exports.Metadata = Metadata
module.exports.BlockingReader = BlockingReader
module.exports.Reader = Reader
module.exports.BlockingWriter = BlockingWriter
module.exports.Writer = Writer
module.exports.Lister = Lister
module.exports.BlockingLister = BlockingLister
module.exports.Layer = Layer
module.exports.RetryLayer = RetryLayer
