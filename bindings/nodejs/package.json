{"name": "opendal", "author": "Apache OpenDAL <<EMAIL>>", "version": "0.47.7", "license": "Apache-2.0", "main": "index.js", "types": "index.d.ts", "description": "Apache OpenDAL: Access data freely", "repository": "**************/apache/opendal.git", "napi": {"name": "opendal", "package": {"name": "@opendal/lib"}, "triples": {"defaults": true, "additional": ["aarch64-apple-darwin", "aarch64-unknown-linux-gnu", "aarch64-unknown-linux-musl", "aarch64-pc-windows-msvc"]}}, "keywords": ["api", "fs", "file system", "gcs", "ghac", "http", "ipmfs", "memory", "obs", "oss", "s3", "storage", "webdav", "webhdfs"], "files": ["index.d.ts", "index.js", "generated.d.ts", "generated.js", "LICENSE", "NOTICE"], "devDependencies": {"@aws-sdk/client-s3": "^3.468.0", "@napi-rs/cli": "^2.18.3", "@swc-node/register": "^1.6.2", "@swc/core": "^1.3.38", "@types/node": "^18.14.5", "@types/react": "^18.2.48", "benny": "^3.7.1", "dotenv": "^16.0.3", "prettier": "^2.8.4", "typedoc": "^0.25", "typescript": "^5.0.2", "vitest": "^1.6.0"}, "engines": {"node": ">= 10"}, "scripts": {"build": "napi build --platform --features \"${NAPI_FEATURES:-}\" --target \"${NAPI_TARGET:-}\" --release --js generated.js --dts generated.d.ts && node ./scripts/header.js", "build:debug": "napi build --platform --features \"${NAPI_FEATURES:-}\" --target \"${NAPI_TARGET:-}\" --js generated.js --dts generated.d.ts && node ./scripts/header.js", "build:theme": "tsc -p ./tsconfig.theme.json", "docs": "typedoc", "format": "prettier --write .", "test": "vitest", "bench": "node -r dotenv/config ./benchmark/node.js dotenv_config_path=./.env", "bench:deno": "deno bench ./benchmark/deno.ts --reload=npm:opendal --allow-read --allow-ffi --allow-net --allow-env", "prepublishOnly": "napi prepublish -t npm"}, "prettier": {"overrides": [{"files": "./**/*.{js,ts,mjs,jsx,tsx}", "options": {"arrowParens": "always", "parser": "typescript", "printWidth": 120, "semi": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "all"}}]}, "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "packageManager": "pnpm@8.11.0"}