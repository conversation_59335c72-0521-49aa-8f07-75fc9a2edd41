# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

# frozen_string_literal: true

source "https://rubygems.org"

# Includes runtime dependencies from opendal.gemspec
gemspec

group :development, :test do
  gem "rake", ">= 13.0"
  gem "rb_sys", "~> 0.9.102" # for Makefile generation in extconf.rb
  gem "rake-compiler", "~> 1.2.0"
  gem "minitest", "~> 5.25.0"
  gem "minitest-reporters", "~> 1.7.1"
  gem "activesupport", "~> 7.2.1"
  gem "standard", "~> 1.3"
end
