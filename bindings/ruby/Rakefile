# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

# frozen_string_literal: true

require "bundler/gem_tasks"
require "rake/testtask"
require "rake/extensiontask"
require "standard/rake"

GEMSPEC = Gem::Specification.load("opendal.gemspec")
CRATE_PACKAGE_NAME = "opendal-ruby"

Rake::ExtensionTask.new(CRATE_PACKAGE_NAME, GEMSPEC) do |ext|
  ext.name = "opendal_ruby"
  ext.ext_dir = "."
  ext.lib_dir = "lib/opendal_ruby"
end

Rake::Task[:test].prerequisites << :compile

Rake::TestTask.new do |t|
  t.libs << "lib"
  t.libs << "test"
  t.pattern = "test/**/*_test.rb"
end

task default: %i[clobber compile test standard]
