<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.apache</groupId>
        <artifactId>apache</artifactId>
        <version>29</version>
    </parent>

    <groupId>org.apache.opendal</groupId>
    <artifactId>opendal</artifactId>
    <version>0.47.4</version>

    <name>Apache OpenDAL™</name>
    <description>
        Apache OpenDAL™ is a data access layer that allows users to
        easily and efficiently retrieve data from various storage services in a unified way.
    </description>

    <url>https://opendal.apache.org</url>
    <mailingLists>
        <mailingList>
            <name>Develop List</name>
            <subscribe><EMAIL></subscribe>
            <unsubscribe><EMAIL></unsubscribe>
            <post><EMAIL></post>
            <archive>https://lists.apache.org/list.html?<EMAIL></archive>
        </mailingList>
    </mailingLists>

    <properties>
        <!-- This value is for Reproducible Builds. It will be updated by Maven Release Plugin during releases. -->
        <project.build.outputTimestamp>10</project.build.outputTimestamp>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>

        <!-- customized properties -->
        <cargo-build.profile>dev</cargo-build.profile>
        <cargo-build.features>default</cargo-build.features>
        <cargo-build.enableZigbuild>false</cargo-build.enableZigbuild>
        <cargo-build.target/> <!-- override cargo build target; e.g., use musl instead -->
        <jni.classifier>${os.detected.classifier}</jni.classifier>

        <!-- library dependencies -->
        <assertj.version>3.23.1</assertj.version>
        <commons-io.version>2.16.1</commons-io.version>
        <dotenv.version>2.3.2</dotenv.version>
        <lombok.version>1.18.34</lombok.version>
        <slf4j.version>2.0.7</slf4j.version>
        <httpclient.version>5.2</httpclient.version>

        <!-- plugins dependencies -->
        <maven-surefire-plugin.version>3.1.2</maven-surefire-plugin.version>
        <maven-compiler-plugin.version>3.13.0</maven-compiler-plugin.version>
        <exec-maven-plugin.version>3.1.0</exec-maven-plugin.version>
        <os-maven-plugin.version>1.7.0</os-maven-plugin.version>
        <palantir-java-format.version>2.36.0</palantir-java-format.version>
        <spotless.version>2.39.0</spotless.version>
        <maven-javadoc-plugin.version>3.6.3</maven-javadoc-plugin.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.junit</groupId>
                <artifactId>junit-bom</artifactId>
                <version>5.9.2</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.assertj</groupId>
                <artifactId>assertj-core</artifactId>
                <version>${assertj.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-simple</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.cdimascio</groupId>
                <artifactId>dotenv-java</artifactId>
                <version>${dotenv.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents.client5</groupId>
                <artifactId>httpclient5</artifactId>
                <version>${httpclient.version}</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-suite</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-simple</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.github.cdimascio</groupId>
            <artifactId>dotenv-java</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents.client5</groupId>
            <artifactId>httpclient5</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
                <filtering>true</filtering>
            </testResource>
        </testResources>

        <extensions>
            <extension>
                <groupId>kr.motd.maven</groupId>
                <artifactId>os-maven-plugin</artifactId>
                <version>${os-maven-plugin.version}</version>
            </extension>
        </extensions>

        <plugins>
            <plugin>
                <artifactId>exec-maven-plugin</artifactId>
                <groupId>org.codehaus.mojo</groupId>
                <version>${exec-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <id>compile-native-code</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                        <configuration>
                            <executable>python3</executable>
                            <arguments>
                                <argument>${project.basedir}/tools/build.py</argument>
                                <argument>--classifier</argument>
                                <argument>${jni.classifier}</argument>
                                <argument>--target</argument>
                                <argument>${cargo-build.target}</argument>
                                <argument>--profile</argument>
                                <argument>${cargo-build.profile}</argument>
                                <argument>--features</argument>
                                <argument>${cargo-build.features}</argument>
                                <argument>--enable-zigbuild</argument>
                                <argument>${cargo-build.enableZigbuild}</argument>
                            </arguments>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <executions>
                    <!--  Generate the fallback JAR that does not contain the native library.  -->
                    <execution>
                        <id>default-jar</id>
                        <configuration>
                            <excludes>
                                <exclude>native/**</exclude>
                            </excludes>
                        </configuration>
                    </execution>
                    <!--  Generate the JAR that contains the native library in it.  -->
                    <execution>
                        <id>native-jar</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                        <configuration>
                            <classifier>${jni.classifier}</classifier>
                            <includes>
                                <include>native/**</include>
                            </includes>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>com.diffplug.spotless</groupId>
                <artifactId>spotless-maven-plugin</artifactId>
                <version>${spotless.version}</version>
                <configuration>
                    <java>
                        <palantirJavaFormat>
                            <version>${palantir-java-format.version}</version>
                        </palantirJavaFormat>
                        <!-- static imports first, then others, no blank lines  -->
                        <importOrder>
                            <order>\#|</order>
                        </importOrder>
                        <removeUnusedImports/>
                        <trimTrailingWhitespace/>
                        <endWithNewline/>
                    </java>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>${maven-javadoc-plugin.version}</version>
                <configuration>
                    <bottom>
                        Copyright © 2022-2024, The Apache Software Foundation. Apache OpenDAL, OpenDAL, and Apache
                        are either registered trademarks or trademarks of the Apache Software Foundation.
                    </bottom>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
