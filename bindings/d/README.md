# Apache OpenDAL™ D Binding (WIP)

![](https://img.shields.io/badge/status-unreleased-red)

![](https://github.com/apache/opendal/assets/5351546/87bbf6e5-f19e-449a-b368-3e283016c887)

## Build

To compile OpenDAL from source code, you need:

- [dmd/ldc/gdc](https://dlang.org/download)

```bash
# build libopendal_c (underneath call make -C ../c)
dub build -b release
# build and run unit tests
dub test
```

## License and Trademarks

Licensed under the Apache License, Version 2.0: http://www.apache.org/licenses/LICENSE-2.0

Apache OpenDAL, OpenDAL, and Apache are either registered trademarks or trademarks of the Apache Software Foundation.
