# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

[package]
categories = ["filesystem"]
description = "OpenDAL Gateway"
keywords = ["storage", "data", "s3", "fs", "azblob"]
name = "oay"

authors = ["Apache OpenDAL <<EMAIL>>"]
edition = "2021"
homepage = "https://opendal.apache.org/"
license = "Apache-2.0"
repository = "https://github.com/apache/opendal"
rust-version = "1.75"
version = "0.41.13"

[features]
default = ["frontends-webdav", "frontends-s3"]

frontends-s3 = []
frontends-webdav = [
  "dep:dav-server",
  "dep:dav-server-opendalfs",
  "dep:futures-util",
]

[dependencies]
anyhow = "1"
axum = "0.7"
chrono = "0.4.31"
dav-server = { version = "0.7", optional = true }
dav-server-opendalfs = { version = "0.2.1", path = "../../integrations/dav-server", optional = true }
futures-util = { version = "0.3.29", optional = true }
opendal = { version = "0.50.0", path = "../../core", features = [
  "services-fs",
] }
quick-xml = { version = "0.36", features = ["serialize", "overlapped-lists"] }
serde = { version = "1", features = ["derive"] }
tokio = { version = "1.39", features = [
  "fs",
  "macros",
  "rt-multi-thread",
  "io-std",
] }
toml = "0.8.12"
tower = { version = "0.4", features = ["util"] }
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
