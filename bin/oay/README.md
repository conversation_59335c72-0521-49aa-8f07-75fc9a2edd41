# Apache OpenDAL™ Oay

[build status]: https://img.shields.io/github/actions/workflow/status/apache/opendal/ci_bin_oay.yml?branch=main
[actions]: https://github.com/apache/opendal/actions?query=branch%3Amain
[latest version]: https://img.shields.io/crates/v/oay.svg
[crates.io]: https://crates.io/crates/oay
[crate downloads]: https://img.shields.io/crates/d/oay.svg
[chat]: https://img.shields.io/discord/1081052318650339399
[discord]: https://opendal.apache.org/discord

`oay` is an OpenDAL Gateway that empowers users to access data through their preferred APIs.

## Goal

Allow users to access different storage backend through their preferred APIs.

![](https://user-images.githubusercontent.com/5351546/233089393-b4ce41df-3236-4dc7-969d-fa00468ae095.png)

## Status

Our first milestone is to provide S3 APIs.

### S3 API

Only `list_object_v2` with `start_after` is supported.

## Branding

The first and most prominent mentions must use the full form: **Apache OpenDAL™** of the name for any individual usage (webpage, handout, slides, etc.) Depending on the context and writing style, you should use the full form of the name sufficiently often to ensure that readers clearly understand the association of both the OpenDAL project and the OpenDAL software product to the ASF as the parent organization.

For more details, see the [Apache Product Name Usage Guide](https://www.apache.org/foundation/marks/guide).

## License and Trademarks

Licensed under the Apache License, Version 2.0: http://www.apache.org/licenses/LICENSE-2.0

Apache OpenDAL, OpenDAL, and Apache are either registered trademarks or trademarks of the Apache Software Foundation.
