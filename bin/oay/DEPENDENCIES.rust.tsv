crate	0BSD	Apache-2.0	Apache-2.0 WITH LLVM-exception	BSD-3-Clause	BSL-1.0	ISC	MIT	MPL-2.0	OpenSSL	Unicode-DFS-2016	Unlicense	Zlib
addr2line@0.24.2		X					X					
adler2@2.0.0	X	X					X					
aho-corasick@1.1.3							X				X	
allocator-api2@0.2.18		X					X					
android-tzdata@0.1.1		X					X					
android_system_properties@0.1.5		X					X					
anyhow@1.0.90		X					X					
async-trait@0.1.83		X					X					
autocfg@1.4.0		X					X					
axum@0.7.7							X					
axum-core@0.4.5							X					
backon@1.2.0		X										
backtrace@0.3.74		X					X					
base64@0.21.7		X					X					
base64@0.22.1		X					X					
bitflags@2.6.0		X					X					
block-buffer@0.10.4		X					X					
bumpalo@3.16.0		X					X					
bytes@1.7.2							X					
cc@1.1.31		X					X					
cfg-if@1.0.0		X					X					
chrono@0.4.38		X					X					
core-foundation-sys@0.8.7		X					X					
cpufeatures@0.2.14		X					X					
crypto-common@0.1.6		X					X					
dav-server@0.7.0		X										
dav-server-opendalfs@0.2.2		X										
deranged@0.3.11		X					X					
digest@0.10.7		X					X					
equivalent@1.0.1		X					X					
fastrand@2.1.1		X					X					
flagset@0.4.6		X										
fnv@1.0.7		X					X					
foldhash@0.1.3												X
form_urlencoded@1.2.1		X					X					
futures@0.3.31		X					X					
futures-channel@0.3.31		X					X					
futures-core@0.3.31		X					X					
futures-executor@0.3.31		X					X					
futures-io@0.3.31		X					X					
futures-macro@0.3.31		X					X					
futures-sink@0.3.31		X					X					
futures-task@0.3.31		X					X					
futures-util@0.3.31		X					X					
generic-array@0.14.7							X					
getrandom@0.2.15		X					X					
gimli@0.31.1		X					X					
gloo-timers@0.3.0		X					X					
hashbrown@0.15.0		X					X					
headers@0.4.0							X					
headers-core@0.3.0							X					
hermit-abi@0.3.9		X					X					
htmlescape@0.3.1		X					X	X				
http@1.1.0		X					X					
http-body@1.0.1							X					
http-body-util@0.1.2							X					
httparse@1.9.5		X					X					
httpdate@1.0.3		X					X					
hyper@1.5.0							X					
hyper-rustls@0.27.3		X				X	X					
hyper-util@0.1.9							X					
iana-time-zone@0.1.61		X					X					
iana-time-zone-haiku@0.1.2		X					X					
idna@0.5.0		X					X					
indexmap@2.6.0		X					X					
ipnet@2.10.1		X					X					
itoa@1.0.11		X					X					
js-sys@0.3.72		X					X					
lazy_static@1.5.0		X					X					
libc@0.2.161		X					X					
lock_api@0.4.12		X					X					
log@0.4.22		X					X					
lru@0.12.5							X					
matchers@0.1.0							X					
matchit@0.7.3				X			X					
md-5@0.10.6		X					X					
memchr@2.7.4							X				X	
mime@0.3.17		X					X					
mime_guess@2.0.5							X					
miniz_oxide@0.8.0		X					X					X
mio@1.0.2							X					
nu-ansi-term@0.46.0							X					
num-conv@0.1.0		X					X					
num-traits@0.2.19		X					X					
oay@0.41.13		X										
object@0.36.5		X					X					
once_cell@1.20.2		X					X					
opendal@0.50.2		X										
overload@0.1.1							X					
parking_lot@0.12.3		X					X					
parking_lot_core@0.9.10		X					X					
percent-encoding@2.3.1		X					X					
pin-project@1.1.6		X					X					
pin-project-internal@1.1.6		X					X					
pin-project-lite@0.2.14		X					X					
pin-utils@0.1.0		X					X					
powerfmt@0.2.0		X					X					
proc-macro2@1.0.88		X					X					
quick-xml@0.36.2							X					
quote@1.0.37		X					X					
redox_syscall@0.5.7							X					
regex@1.11.0		X					X					
regex-automata@0.1.10							X				X	
regex-automata@0.4.8		X					X					
regex-syntax@0.6.29		X					X					
regex-syntax@0.8.5		X					X					
reqwest@0.12.8		X					X					
ring@0.17.8									X			
rustc-demangle@0.1.24		X					X					
rustls@0.23.15		X				X	X					
rustls-pemfile@2.2.0		X				X	X					
rustls-pki-types@1.10.0		X					X					
rustls-webpki@0.102.8						X						
rustversion@1.0.18		X					X					
ryu@1.0.18		X			X							
scopeguard@1.2.0		X					X					
serde@1.0.210		X					X					
serde_derive@1.0.210		X					X					
serde_json@1.0.132		X					X					
serde_path_to_error@0.1.16		X					X					
serde_spanned@0.6.8		X					X					
serde_urlencoded@0.7.1		X					X					
sha1@0.10.6		X					X					
sharded-slab@0.1.7							X					
shlex@1.3.0		X					X					
slab@0.4.9							X					
smallvec@1.13.2		X					X					
socket2@0.5.7		X					X					
spin@0.9.8							X					
subtle@2.6.1				X								
syn@2.0.81		X					X					
sync_wrapper@0.1.2		X										
sync_wrapper@1.0.1		X										
thread_local@1.1.8		X					X					
time@0.3.36		X					X					
time-core@0.1.2		X					X					
time-macros@0.2.18		X					X					
tinyvec@1.8.0		X					X					X
tinyvec_macros@0.1.1		X					X					X
tokio@1.40.0							X					
tokio-macros@2.4.0							X					
tokio-rustls@0.26.0		X					X					
tokio-util@0.7.12							X					
toml@0.8.19		X					X					
toml_datetime@0.6.8		X					X					
toml_edit@0.22.22		X					X					
tower@0.4.13							X					
tower@0.5.1							X					
tower-layer@0.3.3							X					
tower-service@0.3.3							X					
tracing@0.1.40							X					
tracing-attributes@0.1.27							X					
tracing-core@0.1.32							X					
tracing-log@0.2.0							X					
tracing-subscriber@0.3.18							X					
try-lock@0.2.5							X					
typenum@1.17.0		X					X					
unicase@2.8.0		X					X					
unicode-bidi@0.3.17		X					X					
unicode-ident@1.0.13		X					X			X		
unicode-normalization@0.1.24		X					X					
untrusted@0.9.0						X						
url@2.5.2		X					X					
uuid@1.11.0		X					X					
valuable@0.1.0							X					
version_check@0.9.5		X					X					
want@0.3.1							X					
wasi@0.11.0+wasi-snapshot-preview1		X	X				X					
wasm-bindgen@0.2.95		X					X					
wasm-bindgen-backend@0.2.95		X					X					
wasm-bindgen-futures@0.4.45		X					X					
wasm-bindgen-macro@0.2.95		X					X					
wasm-bindgen-macro-support@0.2.95		X					X					
wasm-bindgen-shared@0.2.95		X					X					
wasm-streams@0.4.1		X					X					
web-sys@0.3.72		X					X					
webpki-roots@0.26.6								X				
winapi@0.3.9		X					X					
winapi-i686-pc-windows-gnu@0.4.0		X					X					
winapi-x86_64-pc-windows-gnu@0.4.0		X					X					
windows-core@0.52.0		X					X					
windows-registry@0.2.0		X					X					
windows-result@0.2.0		X					X					
windows-strings@0.1.0		X					X					
windows-sys@0.52.0		X					X					
windows-targets@0.52.6		X					X					
windows_aarch64_gnullvm@0.52.6		X					X					
windows_aarch64_msvc@0.52.6		X					X					
windows_i686_gnu@0.52.6		X					X					
windows_i686_gnullvm@0.52.6		X					X					
windows_i686_msvc@0.52.6		X					X					
windows_x86_64_gnu@0.52.6		X					X					
windows_x86_64_gnullvm@0.52.6		X					X					
windows_x86_64_msvc@0.52.6		X					X					
winnow@0.6.20							X					
xml-rs@0.8.22							X					
xmltree@0.10.3							X					
zeroize@1.8.1		X					X					
