# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

[package]
categories = ["filesystem"]
description = "OpenDAL Command Line Interface"
keywords = ["storage", "data", "s3", "fs", "azblob"]
name = "oli"

authors = ["Apache OpenDAL <<EMAIL>>"]
edition = "2021"
homepage = "https://opendal.apache.org/"
license = "Apache-2.0"
repository = "https://github.com/apache/opendal"
rust-version = "1.75"
version = "0.41.13"

[features]
# Enable services dashmap support
services-dashmap = ["opendal/services-dashmap"]
# Enable services etcd support
services-etcd = ["opendal/services-etcd"]
# Enable services ftp support
services-ftp = ["opendal/services-ftp"]
# Enable services hdfs support
services-hdfs = ["opendal/services-hdfs"]
# Enable services ipfs support
services-ipfs = ["opendal/services-ipfs"]
# Enable services memcached support
services-memcached = ["opendal/services-memcached"]
# Enable services mini-moka support
services-mini-moka = ["opendal/services-mini-moka"]
# Enable services moka support
services-moka = ["opendal/services-moka"]
# Enable services redis support
services-redis = ["opendal/services-redis"]
# Enable services rocksdb support
services-rocksdb = ["opendal/services-rocksdb"]
# Enable services sled support
services-sled = ["opendal/services-sled"]

[dependencies]
anyhow = "1"
clap = { version = "4", features = ["cargo", "string", "derive", "deprecated"] }
dirs = "5.0.1"
futures = "0.3"
indicatif = "0.17.9"
opendal = { version = "0.50.0", path = "../../core", features = [
  # These are default features before v0.46. TODO: change to optional features
  "services-azblob",
  "services-azdls",
  "services-cos",
  "services-fs",
  "services-gcs",
  "services-ghac",
  "services-http",
  "services-ipmfs",
  "services-memory",
  "services-obs",
  "services-oss",
  "services-s3",
  "services-webdav",
  "services-webhdfs",
  "services-azfile",
] }
serde = { version = "1", features = ["derive"] }
tokio = { version = "1.39", features = [
  "fs",
  "macros",
  "rt-multi-thread",
  "io-std",
] }
toml = "0.8.12"
url = "2.5.0"

[dev-dependencies]
assert_cmd = "2"
tempfile = "3.10.1"
