# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

[package]
categories = ["filesystem"]
description = "OpenDAL File System"
keywords = ["storage", "data", "s3", "fs", "azblob"]
name = "ofs"
version = "0.0.14"

authors = ["Apache OpenDAL <<EMAIL>>"]
edition = "2021"
homepage = "https://opendal.apache.org/"
license = "Apache-2.0"
repository = "https://github.com/apache/opendal"
rust-version = "1.75"

[dependencies]
anyhow = "1"
clap = { version = "4.5.18", features = ["derive", "env"] }
env_logger = "0.11.2"
log = "0.4.21"
opendal = { version = "0.50.0", path = "../../core" }
tokio = { version = "1.37.0", features = [
  "fs",
  "macros",
  "rt-multi-thread",
  "io-std",
  "signal",
] }
url = "2.5.0"

[target.'cfg(any(target_os = "linux", target_os = "freebsd", target_os = "macos"))'.dependencies]
fuse3 = { version = "0.8.1", "features" = ["tokio-runtime", "unprivileged"] }
fuse3_opendal = { version = "0.0.9", path = "../../integrations/fuse3" }
libc = "0.2.154"
nix = { version = "0.29.0", features = ["user"] }

[target.'cfg(target_os = "windows")'.dependencies]
cloud-filter = { version = "0.0.5" }
cloud_filter_opendal = { version = "0.0.3", path = "../../integrations/cloud_filter" }

[features]
default = ["services-fs", "services-s3"]
services-fs = ["opendal/services-fs"]
services-s3 = ["opendal/services-s3"]

[dev-dependencies]
opendal = { version = "0.50.0", path = "../../core", features = ["tests"] }
tempfile = "3.10.1"
test-context = "0.3.0"
walkdir = "2.5.0"
