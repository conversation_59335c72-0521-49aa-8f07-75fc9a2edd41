# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

[package]
description = "OpenDAL Benchmark vs fs"
edition = "2021"
license = "Apache-2.0"
name = "opendal-benchmark-vs-fs"
publish = false
rust-version = "1.75"
version = "0.0.0"

[dependencies]
criterion = { version = "0.5", features = ["async", "async_tokio"] }
opendal = { path = "../..", features = ["tests"] }
rand = "0.8"
tokio = { version = "1", features = ["full"] }
uuid = { version = "1", features = ["v4"] }
