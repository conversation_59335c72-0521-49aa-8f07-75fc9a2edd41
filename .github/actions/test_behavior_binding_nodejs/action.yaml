# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: Test Core
description: 'Test Core with given setup and service'
inputs:
  setup:
    description: "The setup action for test"
  service:
    description: "The service to test"
  feature:
    description: "The feature to test"

runs:
  using: "composite"
  steps:
    - name: Setup
      shell: bash
      run: |
        mkdir -p ./dynamic_test_binding_nodejs &&
        cat <<EOF >./dynamic_test_binding_nodejs/action.yml
        runs:
          using: composite
          steps:
          - name: Setup Test
            uses: ./.github/services/${{ inputs.service }}/${{ inputs.setup }}
          - name: Run Test Binding NodeJS
            shell: bash
            working-directory: bindings/nodejs
            run: |
                pnpm build:debug
                pnpm test
            env:
              NAPI_FEATURES: ${{ inputs.feature }}
              OPENDAL_TEST: ${{ inputs.service }}
        EOF
    - name: Run
      uses: ./dynamic_test_binding_nodejs
