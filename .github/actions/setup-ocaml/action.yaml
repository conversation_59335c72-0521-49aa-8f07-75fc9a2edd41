# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: Setup OCaml
description: 'Prepare OCaml Build Environment'
inputs:
  need-depext:
    description: "Enable the automation feature for opam depext."
  need-pin:
    description: "Enable the automation feature for opam pin."

runs:
  using: "composite"
  steps:
    - name: Setup OCaml
      uses: ocaml/setup-ocaml@v2
      with:
        ocaml-compiler: 4.14
        opam-depext: ${{inputs.need-depext == true}}
        opam-pin: ${{inputs.need-pin == true}}

    - name: Set Opam env
      shell: bash
      run: opam env | tr '\n' ' ' >> $GITHUB_ENV
    - name: Add Opam switch to PATH
      shell: bash
      run: opam var bin >> $GITHUB_PATH
