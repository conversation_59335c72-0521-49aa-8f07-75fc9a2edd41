# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: Bug Report
description: Create a report to help us improve
title: "bug: "
labels: ["bug"]
body:
  - type: markdown
    attributes:
      value: "Thank you for taking the time to report a bug. Please provide as much information as possible to help us understand and resolve the issue."

  - type: textarea
    id: describe-bug
    attributes:
      label: Describe the bug
      description: "A clear and concise description of what the bug is."
      placeholder: "Describe the bug..."
    validations:
      required: true

  - type: textarea
    id: steps-to-reproduce
    attributes:
      label: Steps to Reproduce
      description: "Steps to reproduce the behavior:"
    validations:
      required: true

  - type: textarea
    id: expected-behavior
    attributes:
      label: Expected Behavior
      description: "A clear and concise description of what you expected to happen."
      placeholder: "Explain what you expected to happen..."
    validations:
      required: true

  - type: textarea
    id: additional-context
    attributes:
      label: Additional Context
      description: "Add any other context about the problem here."
      placeholder: "Additional details..."
    validations:
      required: false

  - type: markdown
    attributes:
      value: "Please make sure to include any relevant information such as screenshots, logs, or code snippets that may help in diagnosing the issue."

  - type: checkboxes
    id: willing-to-submit-pr
    attributes:
      label: "Are you willing to submit a PR to fix this bug?"
      description: "Let us know if you are willing to contribute a fix by submitting a Pull Request."
      options:
        - label: "Yes, I would like to submit a PR."
