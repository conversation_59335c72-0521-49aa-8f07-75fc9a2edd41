# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: azfile
description: 'Behavior test for Azfi<PERSON>'

runs:
  using: "composite"
  steps:
    - name: Setup
      uses: 1password/load-secrets-action@v1
      with:
        export-env: true
      env:
        OPENDAL_AZFILE_ACCOUNT_NAME: op://services/azfile/account_name
        OPENDAL_AZFILE_ENDPOINT: op://services/azfile/endpoint
        OPENDAL_AZFILE_ACCOUNT_KEY: op://services/azfile/account_key
        OPENDAL_AZFILE_SHARE_NAME: op://services/azfile/share_name

    - name: Add extra settings
      shell: bash
      run: |
        cat << EOF >> $GITHUB_ENV
        OPENDAL_AZFILE_ROOT=test/
        EOF
