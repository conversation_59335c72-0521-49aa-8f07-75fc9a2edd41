# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: seafile
description: "Behavior test for Seafile"

runs:
  using: "composite"
  steps:
    - name: Setup Seafile service
      shell: bash
      working-directory: fixtures/seafile
      run: |
        docker compose -f docker-compose-seafile.yml up -d --wait

    - name: Create test token and setup test library
      shell: bash
      run: |
        token=$(curl --location --request POST -d "username=<EMAIL>&password=asecret" http://127.0.0.1:80/api2/auth-token/ | awk -F '"' '/token/{print $4}')
        curl --location --request POST -d 'name=test' -H "Authorization: Token $token" http://127.0.0.1:80/api2/repos/

    - name: Set environment variables
      shell: bash
      run: |
        cat << EOF >> $GITHUB_ENV
        OPENDAL_SEAFILE_ENDPOINT=http://127.0.0.1:80
        OPENDAL_SEAFILE_USERNAME=<EMAIL>
        OPENDAL_SEAFILE_PASSWORD=asecret
        OPENDAL_SEAFILE_REPO_NAME=test
        OPENDAL_SEAFILE_ROOT=/
        EOF
