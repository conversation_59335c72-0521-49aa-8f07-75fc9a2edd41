# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: dropbox
description: 'Behavior test for dropbox'

runs:
  using: "composite"
  steps:
    - name: Setup credentials
      uses: 1password/load-secrets-action@v1
      with:
        export-env: true
      env:
        OPENDAL_DROPBOX_ROOT: op://services/dropbox/root
        OPENDAL_DROPBOX_REFRESH_TOKEN: op://services/dropbox/refresh_token
        OPENDAL_DROPBOX_CLIENT_ID: op://services/dropbox/client_id
        OPENDAL_DROPBOX_CLIENT_SECRET: op://services/dropbox/client_secret
    - name: Setup test threads
      shell: bash
      run: |
        cat << EOF >> $GITHUB_ENV
        RUST_TEST_THREADS=1
        EOF
