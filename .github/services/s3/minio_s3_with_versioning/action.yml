# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: minio_s3_with_versioning
description: 'Behavior test for Minio S3 with bucket versioning enabled'

runs:
  using: "composite"
  steps:
    - name: Setup MinIO Server
      shell: bash
      working-directory: fixtures/s3
      run: docker compose -f docker-compose-minio.yml up -d --wait
    - name: Setup test bucket
      shell: bash
      env:
        AWS_ACCESS_KEY_ID: "minioadmin"
        AWS_SECRET_ACCESS_KEY: "minioadmin"
        AWS_EC2_METADATA_DISABLED: "true"
      run: |
        aws --endpoint-url http://127.0.0.1:9000/ s3 mb s3://test
        aws --endpoint-url http://127.0.0.1:9000/ s3api put-bucket-versioning --bucket test --versioning-configuration Status=Enabled

    - name: Setup
      shell: bash
      run: |
        cat << EOF >> $GITHUB_ENV
        OPENDAL_S3_BUCKET=test
        OPENDAL_S3_ENDPOINT=http://127.0.0.1:9000
        OPENDAL_S3_ACCESS_KEY_ID=minioadmin
        OPENDAL_S3_SECRET_ACCESS_KEY=minioadmin
        OPENDAL_S3_REGION=us-east-1
        OPENDAL_S3_ENABLE_VERSIONING=true
        EOF
