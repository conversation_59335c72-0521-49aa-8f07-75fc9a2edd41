# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: r2
description: "Behavior test for Cloudflare R2. This service is sponsored by @Xuanwo."

runs:
  using: "composite"
  steps:
    - name: Setup
      uses: 1password/load-secrets-action@v1
      with:
        export-env: true
      env:
        OPENDAL_S3_BUCKET: op://services/r2/bucket
        OPENDAL_S3_ENDPOINT: op://services/r2/endpoint
        OPENDAL_S3_ACCESS_KEY_ID: op://services/r2/access_key_id
        OPENDAL_S3_SECRET_ACCESS_KEY: op://services/r2/secret_access_key

    # OPENDAL_S3_BATCH_MAX_OPERATIONS is the R2's limitation
    # Refer to https://opendal.apache.org/docs/services/s3#compatible-services for more information
    - name: Add extra settings
      shell: bash
      run: |
        cat << EOF >> $GITHUB_ENV
        OPENDAL_S3_REGION=auto
        OPENDAL_S3_DELETE_MAX_SIZE=700
        OPENDAL_S3_DISABLE_STAT_WITH_OVERRIDE=true
        EOF
