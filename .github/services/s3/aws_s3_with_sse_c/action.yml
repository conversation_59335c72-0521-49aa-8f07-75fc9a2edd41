# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: aws_s3_with_sse_c
description: 'Behavior test for AWS S3 with SSE-C. This service is sponsored by @datafuse_labs.'

runs:
  using: "composite"
  steps:
    - name: Setup
      uses: 1password/load-secrets-action@v1
      with:
        export-env: true
      env:
        OPENDAL_S3_ROOT: op://services/s3/root
        OPENDAL_S3_BUCKET: op://services/s3/bucket
        OPENDAL_S3_ENDPOINT: op://services/s3/endpoint
        OPENDAL_S3_ACCESS_KEY_ID: op://services/s3/access_key_id
        OPENDAL_S3_SECRET_ACCESS_KEY: op://services/s3/secret_access_key
        OPENDAL_S3_REGION: op://services/s3/region

    - name: Add extra settings
      shell: bash
      run: |
        cat << EOF >> $GITHUB_ENV
        OPENDAL_S3_SERVER_SIDE_ENCRYPTION_CUSTOMER_ALGORITHM=AES256
        OPENDAL_S3_SERVER_SIDE_ENCRYPTION_CUSTOMER_KEY=MDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDA=
        OPENDAL_S3_SERVER_SIDE_ENCRYPTION_CUSTOMER_KEY_MD5=zZ5FnqcIqUjVwvWmyog4zw==
        EOF
