# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: ceph_rados_s3_with_versioning
description: 'Behavior test for CEPH OBJECT GATEWAY S3 with bucket versioning enabled'

runs:
  using: "composite"
  steps:
    - name: Setup Ceph Rados Server
      shell: bash
      working-directory: fixtures/s3
      run: docker compose -f docker-compose-ceph-rados.yml up -d --wait

    # ceph/demo has support for CEPH_DEMO_BUCKET, but it doesn't work as expected.
    - name: Create bucket
      shell: bash
      working-directory: fixtures/s3
      run: |
        docker exec ceph-demo s3cmd mb s3://demo
        docker exec ceph-demo s3cmd setversioning s3://demo enable

    - name: Setup
      shell: bash
      run: |
        cat << EOF >> $GITHUB_ENV
        OPENDAL_S3_BUCKET=demo
        OPENDAL_S3_ENDPOINT=http://127.0.0.1:8080
        OPENDAL_S3_ACCESS_KEY_ID=demo
        OPENDAL_S3_SECRET_ACCESS_KEY=demo
        OPENDAL_S3_REGION=us-east-1
        OPENDAL_S3_ENABLE_VERSIONING=true
        OPENDAL_S3_DISABLE_WRITE_WITH_IF_MATCH=on
        EOF
