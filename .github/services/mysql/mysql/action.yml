# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: mysql
description: 'Behavior test for mysql'

runs:
  using: "composite"
  steps:
    - name: Setup MySQL Server
      shell: bash
      working-directory: fixtures/mysql
      run: |
        apt update && apt install -y mysql-client
        docker compose -f docker-compose.yml up -d --wait
        while ! mysql -h localhost -P 3306 --protocol=tcp -u root -p'password' -e "SELECT 1"; do
            echo "Waiting for MySQL..."
            sleep 1
        done
    - name: Setup
      shell: bash
      run: |
        cat << EOF >> $GITHUB_ENV
        OPENDAL_MYSQL_CONNECTION_STRING=mysql://root:password@localhost:3306/testdb
        OPENDAL_MYSQL_TABLE=data
        OPENDAL_MYSQL_KEY_FIELD=key
        OPENDAL_MYSQL_VALUE_FIELD=data
        EOF
