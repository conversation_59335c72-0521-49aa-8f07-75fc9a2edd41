# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: sftp
description: 'Behavior test for SFTP'

runs:
  using: "composite"
  steps:
    - name: Setup Sftp
      shell: bash
      working-directory: fixtures/sftp
      run: |
        docker compose -f docker-compose-sftp.yml up -d --wait
    - name: Setup
      shell: bash
      run: |
        chmod 600 ${{ github.workspace }}/fixtures/sftp/test_ssh_key

        cat << EOF >> $GITHUB_ENV
        OPENDAL_SFTP_ENDPOINT=ssh://127.0.0.1:2222
        OPENDAL_SFTP_ROOT=/upload/sftp_test/
        OPENDAL_SFTP_USER=foo
        OPENDAL_SFTP_KEY=${{ github.workspace }}/fixtures/sftp/test_ssh_key
        OPENDAL_SFTP_KNOWN_HOSTS_STRATEGY=accept
        EOF
    
