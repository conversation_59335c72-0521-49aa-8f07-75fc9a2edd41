# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: huggingface
description: "Behavior test for Huggingface File System"

runs:
  using: "composite"
  steps:
    - name: Setup
      shell: bash
      run: |
        cat << EOF >> $GITHUB_ENV
        OPENDAL_HUGGINGFACE_REPO_TYPE=dataset
        OPENDAL_HUGGINGFACE_REPO_ID=opendal/huggingface-testdata
        OPENDAL_HUGGINGFACE_REVISION=main
        OPENDAL_HUGGINGFACE_ROOT=/testdata/
        OPENDAL_DISABLE_RANDOM_ROOT=true
        EOF
