# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: webhdfs_with_list_batch_disabled
description: 'Behavior test for webhdfs with list batch disabled'

runs:
  using: "composite"
  steps:
    - name: Setup webhdfs
      shell: bash
      working-directory: fixtures/webhdfs
      run: |
        docker compose -f docker-compose-webhdfs.yml up -d --wait
    - name: Setup
      shell: bash
      run: |
        cat << EOF >> $GITHUB_ENV
        OPENDAL_WEBHDFS_ROOT=/
        OPENDAL_WEBHDFS_ENDPOINT=http://127.0.0.1:9870
        OPENDAL_WEBHDFS_ATOMIC_WRITE_DIR=.opendal_tmp/
        OPENDAL_WEBHDFS_DISABLE_LIST_BATCH=true
        EOF
