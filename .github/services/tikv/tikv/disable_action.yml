# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: tikv
description: 'Behavior test for TiKV'

runs:
  using: "composite"
  steps:
    - name: install tiup
      shell: bash
      run: curl --proto '=https' --tlsv1.2 -sSf https://tiup-mirrors.pingcap.com/install.sh | sh

    - name: start tiup playground
      shell: bash
      working-directory: fixtures/tikv
      run: |
        ~/.tiup/bin/tiup install tikv:v7.3.0 pd:v7.3.0
        ~/.tiup/bin/tiup playground v7.3.0 --mode tikv-slim --kv 3 --without-monitor --kv.config tikv.toml --pd.config pd.toml &
        while :; do
          echo "waiting cluster to be ready"
          [[ "$(curl -I http://127.0.0.1:2379/pd/api/v1/regions 2>/dev/null | head -n 1 | cut -d$' ' -f2)" -ne "405" ]] || break
          sleep 1
        done

    - name: Setup
      shell: bash
      run: |
        cat << EOF >> $GITHUB_ENV
        OPENDAL_TIKV_ENDPOINTS=http://127.0.0.1:2379
        OPENDAL_TIKV_INSECURE=true
        EOF
