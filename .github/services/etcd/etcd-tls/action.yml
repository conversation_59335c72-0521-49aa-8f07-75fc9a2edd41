# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: etcd-tls
description: 'Behavior test for etcd-tls'

runs:
  using: "composite"
  steps:
    - name: Copy Etcd Certificate Files
      shell: bash
      working-directory: fixtures
      run: |
        mkdir -p /tmp/etcd
        cp -r `pwd`/etcd/* /tmp/etcd
    - name: Setup etcd tls
      shell: bash
      working-directory: fixtures/etcd
      run: docker compose -f docker-compose-standalone-tls.yml up -d --wait
    - name: Setup
      shell: bash
      run: |
        cat << EOF >> $GITHUB_ENV
        OPENDAL_ETCD_ENDPOINTS=https://127.0.0.1:2379
        OPENDAL_ETCD_ROOT=/tmp/opendal
        OPENDAL_ETCD_CA_PATH=/tmp/etcd/ca.pem
        OPENDAL_ETCD_CERT_PATH=/tmp/etcd/client.pem
        OPENDAL_ETCD_KEY_PATH=/tmp/etcd/client-key.pem
        EOF
