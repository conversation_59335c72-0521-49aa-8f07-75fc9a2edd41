# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: mongodb_with_no_auth
description: 'Behavior test for mongodb with no auth'

runs:
  using: "composite"
  steps:
    - name: Setup MongoDB Server
      shell: bash
      working-directory: fixtures/mongodb
      run: docker compose -f docker-compose-no-auth.yml up -d --wait
    - name: Setup
      shell: bash
      run: |
        cat << EOF >> $GITHUB_ENV
        OPENDAL_MONGODB_CONNECTION_STRING=mongodb://127.0.0.1:27017
        OPENDAL_MONGODB_DATABASE=demo
        OPENDAL_MONGODB_COLLECTION=demo
        EOF
