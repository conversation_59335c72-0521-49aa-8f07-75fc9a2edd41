# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: azdls
description: 'Behavior test for Azure azdls. This service is sponsored by @Xuanwo.'

runs:
  using: "composite"
  steps:
    - name: Setup
      uses: 1password/load-secrets-action@v1
      with:
        export-env: true
      env:
        OPENDAL_AZDLS_FILESYSTEM: op://services/azdls/filesystem
        OPENDAL_AZDLS_ENDPOINT: op://services/azdls/endpoint
        OPENDAL_AZDLS_ACCOUNT_NAME: op://services/azdls/account_name
        OPENDAL_AZDLS_ACCOUNT_KEY: op://services/azdls/account_key
    - name: Add extra settings
      shell: bash
      run: |
        cat << EOF >> $GITHUB_ENV
        OPENDAL_AZDLS_ROOT=test/
        EOF
