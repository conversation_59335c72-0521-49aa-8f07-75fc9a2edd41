# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: swift
description: "Behavior test for OpenStack Swift."

runs:
  using: "composite"
  steps:
    - name: Setup Swift service
      shell: bash
      working-directory: fixtures/swift
      run: |
        docker compose -f docker-compose-swift.yml up -d --wait

    - name: Create environment variables and setup test container
      shell: bash
      run: |
        response=$(curl -i -H 'X-Storage-User: test:tester' -H 'X-Storage-Pass: testing' http://127.0.0.1:8080/auth/v1.0)
        token=$(echo "$response" | grep X-Auth-Token | head -n1 | awk '{print $2}' | tr -d '[:space:]')
        endpoint=$(echo "$response" | grep X-Storage-Url | head -n1 | awk '{print $2}' | tr -d '[:space:]')
        curl --location --request PUT "${endpoint}/testing" --header "X-Auth-Token: $token"
        echo "OPENDAL_SWIFT_TOKEN=${token}" >> $GITHUB_ENV
        echo "OPENDAL_SWIFT_ENDPOINT=${endpoint}" >> $GITHUB_ENV

    - name: Set environment variables
      shell: bash
      run: |
        cat << EOF >> $GITHUB_ENV
        OPENDAL_SWIFT_CONTAINER=testing
        OPENDAL_SWIFT_ROOT=/
        EOF
