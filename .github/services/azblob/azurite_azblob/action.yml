# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: azurite_azblob
description: 'Behavior test for azurite azblob'

runs:
  using: "composite"
  steps:
    - name: Setup azurite azblob service
      shell: bash
      working-directory: fixtures/azblob
      run: docker compose -f docker-compose-azurite.yml up -d --wait
    - name: Setup test bucket
      shell: bash
      run: |
        az storage container create \
            --name test \
            --connection-string "DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://127.0.0.1:10000/devstoreaccount1;"
    - name: Setup
      shell: bash
      run: |
        cat << EOF >> $GITHUB_ENV
        RUST_BACKTRACE=full
        RUST_LOG=debug
        OPENDAL_AZBLOB_CONTAINER=test
        OPENDAL_AZBLOB_ENDPOINT=http://127.0.0.1:10000/devstoreaccount1
        OPENDAL_AZBLOB_ACCOUNT_NAME=devstoreaccount1
        OPENDAL_AZBLOB_ACCOUNT_KEY=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==
        EOF
