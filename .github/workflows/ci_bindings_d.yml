# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: Bindings D CI

on:
  push:
    branches:
      - main
    tags:
      - "*"
  pull_request:
    branches:
      - main
    paths:
      - "core/**"
      - "bindings/c/**"
      - "bindings/d/**"
      - ".github/workflows/ci_bindings_d.yml"
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}-${{ github.event_name }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  test:
    strategy:
      matrix:
      # dmd: base (self-hosting) compiler (frontend & backend)
      # ldc2/ldmd2: (dmd-frontend + LLVM backend) - recommended for MacOS ARM64
        dlang: ["ldc-latest", "dmd-latest"]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: dlang-community/setup-dlang@v2
        with:
          compiler: ${{ matrix.dlang }}

      - name: Setup Rust toolchain
        uses: ./.github/actions/setup

      - name: Build D binding
        working-directory: bindings/d
        run: dub build

      - name: Check diff
        run: git diff --exit-code

      - name: Check
        working-directory: bindings/d
        run: dub lint

      - name: Run tests
        working-directory: bindings/d
        run: dub test && cd test && dub
