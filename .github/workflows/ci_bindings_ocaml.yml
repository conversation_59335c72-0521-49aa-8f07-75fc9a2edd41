# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: Bindings OCaml CI

on:
  push:
    branches:
      - main
    tags:
      - "*"
  pull_request:
    branches:
      - main
    paths:
      - "bindings/ocaml/**"
      - "core/**"
      - ".github/workflows/ci_bindings_ocaml.yml"
  workflow_dispatch:

jobs:
  test:
    # Wait for https://github.com/ocaml/setup-ocaml/issues/872 fixes
    runs-on: ubuntu-22.04

    steps:
      - name: Checkout PR
        uses: actions/checkout@v4

      - name: Cache OPAM dependencies
        uses: actions/cache@v4
        with:
          path: ~/.opam
          key: ${{ runner.os }}-opam-${{ hashFiles('bindings/ocaml/dune-project') }}
          restore-keys: |
            ${{ runner.os }}-opam-
            ${{ runner.os }}-opam-doc-

      - name: Cache Dune build artifacts
        uses: actions/cache@v4
        with:
          path: bindings/ocaml/_build
          key: ${{ runner.os }}-dune-${{ hashFiles('bindings/ocaml/**/*.{ml,mli,opam}') }}
          restore-keys: |
            ${{ runner.os }}-dune-
            ${{ runner.os }}-dune-doc-

      - name: Setup OCaml toolchain
        uses: ./.github/actions/setup-ocaml
        with:
          need-depext: true
          need-pin: true

      - name: Checks
        run: |
          # make sure tests pass
          opam install -y dune ounit2 ocamlformat
          eval $(opam env)
          cd bindings/ocaml
          dune runtest
          dune build @fmt
