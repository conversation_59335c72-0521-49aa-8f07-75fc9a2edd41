# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

# This workflow is used for publish all rust based packages

name: Release Rust Packages

on:
  push:
    tags:
      - "*"
  pull_request:
    branches:
      - main
    paths:
      - ".github/workflows/release_rust.yml"
  workflow_dispatch:

jobs:
  publish:
    runs-on: ubuntu-latest
    strategy:
      # Publish package one by one instead of flooding the registry
      max-parallel: 1
      fail-fast: false
      matrix:
        # Order here is sensitive, as it will be used to determine the order of publishing
        package:
          - "core"
          - "integrations/compat"
          - "integrations/object_store"
          - "integrations/parquet"
          - "integrations/dav-server"
          - "integrations/fuse3"
          - "integrations/unftp-sbe"
          - "integrations/cloud_filter"
          - "bin/oay" # depends on integrations/dav-server
          - "bin/oli"
          - "bin/ofs" # depends on integrations/{fuse3,cloud_filter}
    steps:
      - uses: actions/checkout@v4
      - name: Checkout python env
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"
      - name: Checkout java env
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: "11"

      - name: Setup Rust toolchain
        uses: ./.github/actions/setup
        with:
          need-rocksdb: true
          need-protoc: true

      - name: Dryrun ${{ matrix.package }}
        # Only dryrun test upon core.
        if: matrix.package == 'core'
        working-directory: ${{ matrix.package }}
        run: cargo publish --dry-run
        env:
          LD_LIBRARY_PATH: ${{ env.JAVA_HOME }}/lib/server:${{ env.LD_LIBRARY_PATH }}

      - name: Publish ${{ matrix.package }}
        working-directory: ${{ matrix.package }}
        # Only publish if it's a tag and the tag is not a pre-release
        if: ${{ startsWith(github.ref, 'refs/tags/') && !contains(github.ref, '-') }}
        run: cargo publish --no-verify
        env:
          LD_LIBRARY_PATH: ${{ env.JAVA_HOME }}/lib/server:${{ env.LD_LIBRARY_PATH }}
          CARGO_REGISTRY_TOKEN: ${{ secrets.CARGO_REGISTRY_TOKEN }}
