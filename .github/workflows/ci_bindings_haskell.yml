# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: Bindings Haskell CI

on:
  push:
    branches:
      - main
    tags:
      - '*'
  pull_request:
    branches:
      - main
    paths:
      - "bindings/haskell/**"
      - "core/**"
      - ".github/workflows/ci_bindings_haskell.yml"
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}-${{ github.event_name }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup Haskell toolchain (ghc-9.4.8)
        run: |
          curl --proto '=https' --tlsv1.2 -sSf https://get-ghcup.haskell.org | sh
          ghcup install ghc 9.4.8 --set
          ghcup install cabal --set
          cabal update
      - name: Setup Rust toolchain
        uses: ./.github/actions/setup
      - name: Restore haskell cache
        uses: actions/cache/restore@v4
        with:
          key: ${{ runner.os }}-haskell-${{ hashFiles('**/*.cabal', '**/Setup.hs') }}
          path: |
            ~/.cabal/store
            bindings/haskell/dist-newstyle
          restore-keys: ${{ runner.os }}-haskell-
      - name: Build & Test
        working-directory: "bindings/haskell"
        run: |
          cabal test
      - name: Save haskell cache
        uses: actions/cache/save@v4
        with:
          key: ${{ runner.os }}-haskell-${{ hashFiles('**/*.cabal', '**/Setup.hs') }}
          path: |
            ~/.cabal/store
            bindings/haskell/dist-newstyle

  package:
    runs-on: ubuntu-latest
    if: "startsWith(github.ref, 'refs/tags/')"
    steps:
      - uses: actions/checkout@v4
      - name: Setup Haskell toolchain (ghc-9.2.8)
        run: |
          curl --proto '=https' --tlsv1.2 -sSf https://get-ghcup.haskell.org | sh
          ghcup install ghc 9.2.8 --set
          ghcup install cabal --set
          cabal update
      - name: Setup Rust toolchain
        uses: ./.github/actions/setup
      - name: Restore haskell cache
        uses: actions/cache/restore@v4
        with:
          key: ${{ runner.os }}-haskell-${{ hashFiles('**/*.cabal', '**/Setup.hs') }}
          path: |
            ~/.cabal/store
            bindings/haskell/dist-newstyle
          restore-keys: ${{ runner.os }}-haskell-
      - name: Package
        working-directory: "bindings/haskell"
        run: |
          cargo package --no-verify --target-dir target
          cd target/package
          tar xf opendal-*.crate --strip-components=1
          cabal sdist
      - name: Upload artifact
        uses: actions/upload-artifact@v3
        with:
          name: bindings-haskell-sdist
          path: bindings/haskell/target/package/dist-newstyle/sdist/*.tar.gz

# Disable haskell release until we are ready.
#
#  release:
#    name: Release
#    runs-on: ubuntu-latest
#    if: "startsWith(github.ref, 'refs/tags/') && !contains(github.ref, '-')"
#    needs: [package]
#    steps:
#      - uses: actions/download-artifact@v3
#        with:
#          name: bindings-haskell-sdist
#      - name: Load secret
#        id: op-load-secret
#        uses: 1password/load-secrets-action@v1
#        with:
#          export-env: true
#        env:
#          OP_SERVICE_ACCOUNT_TOKEN: ${{ secrets.OP_SERVICE_ACCOUNT_TOKEN }}
#          OPENDAL_HACKAGE_TOKEN: op://services/hackage/token
#      - name: Publish to Hackage
#        run: |
#          cabal upload -t $OPENDAL_HACKAGE_TOKEN --publish bindings/haskell/target/package/dist-newstyle/sdist/*.tar.gz
