# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: Bindings Java CI

on:
  push:
    branches:
      - main
    tags:
      - '*'
  pull_request:
    branches:
      - main
    paths:
      - "core/**"
      - "bindings/java/**"
      - ".github/workflows/ci_bindings_java.yml"
  schedule:
    - cron: '30 1 * * 1'
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}-${{ github.event_name }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          distribution: 'zulu'
          java-version: '21'
          cache: 'maven'
      - name: Build and check
        working-directory: bindings/java
        run: |
          ./mvnw clean compile spotless:check

  test:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os:
          - ubuntu-latest
          - macos-latest
          - windows-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up JDK 8
        uses: actions/setup-java@v4
        with:
          distribution: 'zulu'
          java-version: '8'
          cache: 'maven'
      - name: Build and test
        working-directory: bindings/java
        # `mvn install` and `mvn artifact:compare` are required to verify reproducible builds:
        # https://maven.apache.org/guides/mini/guide-reproducible-builds.html#how-to-test-my-maven-build-reproducibility
        shell: bash
        run: |
          ./mvnw clean install -DskipTests
          ./mvnw verify artifact:compare
