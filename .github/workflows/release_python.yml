# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: Release Python

on:
  push:
    tags:
      - "*"
  pull_request:
    branches:
      - main
    paths:
      - ".github/workflows/release_python.yml"
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}-${{ github.event_name }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  sdist:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: PyO3/maturin-action@v1
        with:
          working-directory: "bindings/python"
          command: sdist
          args: -o dist
      - name: Upload sdist
        uses: actions/upload-artifact@v3
        with:
          name: wheels
          path: bindings/python/dist

  wheels:
    runs-on: "${{ matrix.os }}"
    strategy:
      matrix:
        include:
          - { os: windows-latest }
          - { os: macos-latest, target: "universal2-apple-darwin" }
          - { os: ubuntu-latest, target: "x86_64" }
          - { os: ubuntu-latest, target: "aarch64" }
          - { os: ubuntu-latest, target: "armv7l" }
    env:
      # Workaround ring 0.17 build issue
      CFLAGS_aarch64_unknown_linux_gnu: "-D__ARM_ARCH=8"
    steps:
      - uses: actions/checkout@v4
      - name: Setup Rust toolchain
        uses: ./.github/actions/setup
      - uses: PyO3/maturin-action@v1
        with:
          working-directory: "bindings/python"
          target: "${{ matrix.target }}"
          command: build
          args: --release -o dist -i python3.11 --features=pyo3/extension-module,services-all,abi3
          sccache: true
          manylinux: auto
      - uses: PyO3/maturin-action@v1
        with:
          working-directory: "bindings/python"
          target: "${{ matrix.target }}"
          command: build
          args: --release -o dist -i python3.10 --features=pyo3/extension-module,services-all
          sccache: true
          manylinux: auto
      - name: Build free-threaded wheels
        uses: PyO3/maturin-action@v1
        with:
          working-directory: "bindings/python"
          target: "${{ matrix.target }}"
          command: build
          args: --release -o dist -i python3.13t --features=pyo3/extension-module,services-all
          sccache: true
          manylinux: auto
      - name: Upload wheels
        uses: actions/upload-artifact@v3
        with:
          name: wheels
          path: bindings/python/dist

  release:
    name: Release
    runs-on: ubuntu-latest
    if: "startsWith(github.ref, 'refs/tags/')"
    permissions:
      contents: read
      id-token: write
    needs: [sdist, wheels]
    steps:
      - uses: actions/download-artifact@v3
        with:
          name: wheels
          path: bindings/python/dist
      - name: Publish to TestPyPI
        uses: pypa/gh-action-pypi-publish@release/v1
        if: "contains(github.ref, '-')"
        with:
          repository-url: https://test.pypi.org/legacy/
          skip-existing: true
          packages-dir: bindings/python/dist
      - name: Publish to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
        if: "!contains(github.ref, '-')"
        with:
          skip-existing: true
          packages-dir: bindings/python/dist
