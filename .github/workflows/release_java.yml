# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: Release Java Binding

on:
  push:
    tags:
      # Staging JARs on Apache Nexus repository for RCs. Read more on
      # https://opendal.apache.org/community/committers/release/#release-maven-artifacts
      - "v[0-9]+.[0-9]+.[0-9]+-rc.[0-9]+"
  pull_request:
    branches:
      - main
    paths:
      - ".github/workflows/release_java.yml"
      - "bindings/java/tools/build.py"
  workflow_dispatch:

jobs:
  stage-snapshot:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        include:
          - os: ubuntu-latest
            classifier: linux-x86_64
          - os: ubuntu-latest
            classifier: linux-aarch_64
          - os: windows-latest
            classifier: windows-x86_64
          - os: macos-latest
            classifier: osx-x86_64
          - os: macos-latest
            classifier: osx-aarch_64
    steps:
      - uses: actions/checkout@v4
      - name: Set up JDK 8
        uses: actions/setup-java@v4
        with:
          distribution: "zulu"
          java-version: "8"
          cache: "maven"
          server-id: apache.releases.https
          server-username: MAVEN_USERNAME
          server-password: MAVEN_CENTRAL_TOKEN
          gpg-private-key: ${{ secrets.GPG_SECRET_KEY }}
          gpg-passphrase: MAVEN_GPG_PASSPHRASE
      - uses: actions/setup-python@v5
        with:
          python-version: "3.11"
      - name: Install Protoc
        uses: arduino/setup-protoc@v3
        with:
          version: "23.4"
          repo-token: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup linux for zigbuild
        if: "contains(matrix.os, 'ubuntu-latest')"
        run: pip install cargo-zigbuild

      - name: Setup linux-aarch_64
        if: "contains(matrix.classifier, 'linux-aarch_64')"
        run: |
          sudo apt-get update
          sudo apt-get install -y gcc-aarch64-linux-gnu g++-aarch64-linux-gnu
          # Setup for cargo
          echo "CARGO_TARGET_AARCH64_UNKNOWN_LINUX_GNU_LINKER=aarch64-linux-gnu-gcc" >> $GITHUB_ENV

      - name: Local staging
        working-directory: bindings/java
        shell: bash
        run: |
          ./mvnw -Papache-release package verify org.sonatype.plugins:nexus-staging-maven-plugin:deploy \
            -DskipTests=true \
            -Djni.classifier=${{ matrix.classifier }} \
            -Dcargo-build.profile=release \
            -Dcargo-build.features=services-all \
            -Dcargo-build.enableZigbuild=true \
            -DaltStagingDirectory=local-staging \
            -DskipRemoteStaging=true \
            -DserverId=apache.releases.https \
            -DnexusUrl=https://repository.apache.org
        env:
          MAVEN_USERNAME: ${{ secrets.NEXUS_STAGE_DEPLOYER_USER }}
          MAVEN_CENTRAL_TOKEN: ${{ secrets.NEXUS_STAGE_DEPLOYER_PW }}
          MAVEN_GPG_PASSPHRASE: ""
      - name: Upload local staging directory
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.classifier }}-local-staging
          path: bindings/java/local-staging
          if-no-files-found: error
          include-hidden-files: true

  deploy-staged-snapshots:
    runs-on: ubuntu-latest
    needs: [stage-snapshot]
    steps:
      - uses: actions/checkout@v4
        with:
          repository: apache/opendal
          ref: ${{ inputs.ref }}
      - name: Set up JDK 8
        uses: actions/setup-java@v4
        with:
          distribution: "zulu"
          java-version: "8"
          cache: "maven"
          server-id: apache.releases.https
          server-username: MAVEN_USERNAME
          server-password: MAVEN_CENTRAL_TOKEN
          gpg-private-key: ${{ secrets.GPG_SECRET_KEY }}
          gpg-passphrase: MAVEN_GPG_PASSPHRASE

      - name: Prepare environment variables
        run: echo "LOCAL_STAGING_DIR=$HOME/local-staging" >> $GITHUB_ENV

      - name: Download windows staging directory
        uses: actions/download-artifact@v4
        with:
          name: windows-x86_64-local-staging
          path: ~/windows-x86_64-local-staging
      - name: Download linux x86_64 staging directory
        uses: actions/download-artifact@v4
        with:
          name: linux-x86_64-local-staging
          path: ~/linux-x86_64-local-staging
      - name: Download linux aarch_64 staging directory
        uses: actions/download-artifact@v4
        with:
          name: linux-aarch_64-local-staging
          path: ~/linux-aarch_64-local-staging
      - name: Download darwin staging directory
        uses: actions/download-artifact@v4
        with:
          name: osx-x86_64-local-staging
          path: ~/osx-x86_64-local-staging
      - name: Download darwin (aarch64) staging directory
        uses: actions/download-artifact@v4
        with:
          name: osx-aarch_64-local-staging
          path: ~/osx-aarch_64-local-staging

      - uses: actions/checkout@v4
        with:
          path: ci-opendal
      - name: Merge staging repositories
        working-directory: ci-opendal
        run: |
          python ./scripts/merge_local_staging.py $LOCAL_STAGING_DIR/staging \
            ~/windows-x86_64-local-staging/staging \
            ~/linux-x86_64-local-staging/staging \
            ~/linux-aarch_64-local-staging/staging \
            ~/osx-x86_64-local-staging/staging \
            ~/osx-aarch_64-local-staging/staging

      - name: Deploy local staged artifacts
        if: ${{ github.event_name != 'pull_request' }}
        working-directory: bindings/java
        run: |
          ./mvnw org.sonatype.plugins:nexus-staging-maven-plugin:deploy-staged \
            -DaltStagingDirectory=$LOCAL_STAGING_DIR \
            -DskipStagingRepositoryClose=true \
            -DserverId=apache.releases.https \
            -DnexusUrl=https://repository.apache.org
        env:
          MAVEN_USERNAME: ${{ secrets.NEXUS_STAGE_DEPLOYER_USER }}
          MAVEN_CENTRAL_TOKEN: ${{ secrets.NEXUS_STAGE_DEPLOYER_PW }}
          MAVEN_GPG_PASSPHRASE: ""
