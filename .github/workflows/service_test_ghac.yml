# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: Service Test Ghac

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main
    paths:
      - "core/src/**"
      - "core/tests/**"
      - "!core/src/docs/**"
      - "!core/src/services/**"
      - "core/src/services/ghac/**"
      - ".github/workflows/service_test_ghac.yml"

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}-${{ github.event_name }}
  cancel-in-progress: true

jobs:
  ghac:
    runs-on: ubuntu-latest
    if: github.event_name == 'push' || !github.event.pull_request.head.repo.fork
    permissions:
      actions: write
    steps:
      - uses: actions/checkout@v4
      - name: Setup Rust toolchain
        uses: ./.github/actions/setup
        with:
          need-nextest: true

      - name: Configure Cache Env
        uses: actions/github-script@v7
        with:
          script: |
            core.exportVariable('ACTIONS_CACHE_URL', process.env.ACTIONS_CACHE_URL || '');
            core.exportVariable('ACTIONS_RUNTIME_TOKEN', process.env.ACTIONS_RUNTIME_TOKEN || '');

      - name: Test
        shell: bash
        working-directory: core
        run: cargo nextest run behavior --features tests,services-ghac
        env:
          OPENDAL_TEST: ghac
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
