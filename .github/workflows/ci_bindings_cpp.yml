# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: Bindings Cpp CI

on:
  push:
    branches:
      - main
    tags:
      - '*'
  pull_request:
    branches:
      - main
    paths:
      - "bindings/cpp/**"
      - "core/**"
      - ".github/workflows/ci_bindings_cpp.yml"
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}-${{ github.event_name }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  test:
    runs-on: ubuntu-24.04
    steps:
      - uses: actions/checkout@v4
      - name: Install dependencies
        run: |
          sudo apt-get update
          sudo apt-get install ninja-build valgrind doxygen

      - name: Setup Rust toolchain
        uses: ./.github/actions/setup

      - name: Build Cpp binding && Run tests
        working-directory: "bindings/cpp"
        run: |
          mkdir build
          cd build
          cmake -GNinja -DOPENDAL_DEV=ON ..
          ninja
          ./opendal_cpp_test

      - name: Run tests with valgrind
        working-directory: "bindings/cpp"
        run: |
          mkdir build-valgrind
          cd build-valgrind
          cmake -GNinja -DOPENDAL_ENABLE_TESTING=ON ..
          ninja
          valgrind --leak-check=full --show-leak-kinds=all --track-origins=yes --verbose ./opendal_cpp_test

      - name: Build Cpp binding with async && Run tests
        working-directory: "bindings/cpp"
        run: |
          mkdir build-async
          cd build-async
          cmake -GNinja -DOPENDAL_DEV=ON -DOPENDAL_ENABLE_ASYNC=ON -DCMAKE_CXX_COMPILER=clang++-18 ..
          ninja
          ./opendal_cpp_test
