# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: Behavior Test Binding Java

on:
  workflow_call:
    inputs:
      os:
        required: true
        type: string
      cases:
        required: true
        type: string

jobs:
  test:
    name: ${{ matrix.cases.service }} / ${{ matrix.cases.setup }}
    runs-on: ${{ inputs.os }}
    strategy:
      fail-fast: false
      matrix:
        cases: ${{ fromJson(inputs.cases) }}
    steps:
      - uses: actions/checkout@v4
      - name: Setup Rust toolchain
        uses: ./.github/actions/setup
        with:
          need-nextest: true
          need-protoc: true
          need-rocksdb: true
          github-token: ${{ secrets.GITHUB_TOKEN }}

      # TODO: 1password is only supported on linux
      #
      # Waiting for https://github.com/1Password/load-secrets-action/issues/46
      - name: Setup 1Password Connect
        if: runner.os == 'Linux'
        uses: 1password/load-secrets-action/configure@v1
        with:
          connect-host: ${{ secrets.OP_CONNECT_HOST }}
          connect-token: ${{ secrets.OP_CONNECT_TOKEN }}

      - name: Test Core
        uses: ./.github/actions/test_behavior_binding_java
        with:
          setup: ${{ matrix.cases.setup }}
          service: ${{ matrix.cases.service }}
          feature: ${{ matrix.cases.feature }}
