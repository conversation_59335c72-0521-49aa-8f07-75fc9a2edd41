# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: Behavior Test Integration Cloud Filter

on:
  push:
    paths:
      - "integrations/cloudfilter/**.rs"
      - "integrations/cloudfilter/Cargo.toml"
      - ".github/workflows/test_behavior_integration_cloud_filter.yml"
  pull_request:
    paths:
      - "integrations/cloudfilter/**.rs"
      - "integrations/cloudfilter/Cargo.toml"
      - ".github/workflows/test_behavior_integration_cloud_filter.yml"

jobs:
  test:
    name: fs / fixture_data
    runs-on: windows-latest
    timeout-minutes: 10

    steps:
      - uses: actions/checkout@v4
      - name: Setup Rust toolchain
        uses: ./.github/actions/setup

      - name: Test Integration CloudFilter
        working-directory: integrations/cloud_filter
        run: cargo test --test behavior
        env:
          OPENDAL_TEST: fs
          OPENDAL_FS_ROOT: ../../fixtures/data
          OPENDAL_DISABLE_RANDOM_ROOT: "true"
