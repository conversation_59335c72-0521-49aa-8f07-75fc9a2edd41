# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: Core CI

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main
    paths:
      - "core/**"
      - ".github/workflows/ci_core.yml"

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}-${{ github.event_name }}
  cancel-in-progress: true

jobs:
  check_docs:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup Rust toolchain
        uses: ./.github/actions/setup
        with:
          need-rocksdb: true
          need-protoc: true
          github-token: ${{ secrets.GITHUB_TOKEN }}

      - name: Checkout java env
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: "11"

      - name: Cargo doc
        working-directory: core
        run: cargo doc --lib --no-deps --all-features

  check_clippy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup Rust toolchain
        uses: ./.github/actions/setup
        with:
          need-rocksdb: true
          need-protoc: true
          github-token: ${{ secrets.GITHUB_TOKEN }}

      - name: Checkout java env
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: "11"

      - name: Cargo clippy
        working-directory: core
        run: cargo clippy --all-targets --all-features -- -D warnings

  check_msrv:
    runs-on: ubuntu-latest
    env:
      # OpenDAL's MSRV is 1.75.
      OPENDAL_MSRV: "1.75"
    steps:
      - uses: actions/checkout@v4
      - name: Setup msrv of rust
        run: |
          rustup toolchain install ${OPENDAL_MSRV}
          rustup component add clippy --toolchain ${OPENDAL_MSRV}
      - name: Check
        working-directory: core
        run: cargo +${OPENDAL_MSRV} clippy -- -D warnings

  build_default_features:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os:
          - ubuntu-latest
          - macos-latest
          - windows-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup Rust toolchain
        uses: ./.github/actions/setup
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
      - name: Build
        working-directory: core
        run: cargo build

  build_all_features:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Checkout python env
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"
      - name: Checkout java env
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: "11"

      - name: Setup Rust toolchain
        uses: ./.github/actions/setup
        with:
          need-rocksdb: true
          need-protoc: true
          github-token: ${{ secrets.GITHUB_TOKEN }}

      - name: Build
        working-directory: core
        run: cargo build --all-features

  build_all_platforms:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os:
          - ubuntu-latest
          - macos-latest
          - windows-latest
    steps:
      - uses: actions/checkout@v4
      - name: Checkout python env
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"
      - name: Checkout java env
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: "11"

      - name: Setup Rust toolchain
        uses: ./.github/actions/setup
        with:
          need-rocksdb: true
          need-protoc: true
          github-token: ${{ secrets.GITHUB_TOKEN }}

      - name: Build
        shell: bash
        working-directory: core
        run: |
          FEATURES=(
            services-alluxio
            services-azblob
            services-azdls
            services-cacache
            services-cos
            services-dashmap
            services-dropbox
            services-etcd
            # FIXME this requires a preinstalled fdb library
            # services-foundationdb
            services-fs
            services-ftp
            services-gcs
            services-gdrive
            services-ghac
            # FIXME how to support HDFS services in other platforms?
            # services-hdfs
            services-http
            services-huggingface
            services-ipfs
            services-ipmfs
            services-memcached
            services-memory
            services-mini-moka
            services-moka
            services-obs
            services-onedrive
            services-oss
            services-persy
            services-postgresql
            services-redb
            services-redis
            # TODO: we need to find ways to using pre-install rocksdb library
            # services-rocksdb
            services-s3
            services-seafile
            # TODO: sftp is known to not work on windows, waiting for https://github.com/apache/opendal/issues/2963
            # services-sftp
            services-sled
            services-swift
            services-supabase
            services-tikv
            services-vercel-artifacts
            services-webdav
            services-webhdfs
          )
          cargo build --features "${FEATURES[*]}"

  # We only support some services(see `available_services` below) for now.
  build_under_wasm:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup Rust toolchain
        uses: ./.github/actions/setup
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
      - name: Build
        working-directory: core
        run: |
          FEATURES=(
            services-azblob
            services-gdrive
            services-s3
          )
          rustup target add wasm32-unknown-unknown
          cargo build --target wasm32-unknown-unknown --no-default-features --features="${FEATURES[*]}"

  unit:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Checkout python env
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"
      - name: Checkout java env
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: "11"

      - name: Setup Rust toolchain
        uses: ./.github/actions/setup
        with:
          need-protoc: true
          need-rocksdb: true
          need-foundationdb: true
          need-nextest: true
          github-token: ${{ secrets.GITHUB_TOKEN }}

      - name: Test
        working-directory: core
        run: |
          cargo nextest run --no-fail-fast --all-features
          cargo test --doc --all-features
        env:
          # Add rocksdb and java lib path to LD_LIBRARY_PATH
          LD_LIBRARY_PATH: /tmp/rocksdb/lib:${{ env.JAVA_HOME }}/lib/server:${{ env.LD_LIBRARY_PATH }}
