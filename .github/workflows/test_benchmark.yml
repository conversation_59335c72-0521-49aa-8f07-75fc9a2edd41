# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: Benchmark Test

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main
    paths:
      - "core/src/**"
      - "core/benches/**"
      - "!core/src/docs/**"
      - ".github/workflows/test_benchmark.yml"
  # `workflow_dispatch` is needed by codspeed for first time trigger
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}-${{ github.event_name }}
  cancel-in-progress: true

jobs:
  benchmark:
    runs-on: ubuntu-latest
    if: github.event_name == 'push' || !github.event.pull_request.head.repo.fork
    steps:
      - uses: actions/checkout@v4
      - name: Setup Rust toolchain
        uses: ./.github/actions/setup
      - name: Setup codspeed
        run: cargo install cargo-codspeed
      - name: Setup Memory env
        uses: ./.github/services/memory/memory
      - name: Install codpseed-criterion-compat
        working-directory: core
        run: cargo add --dev --rename criterion --features async,async_tokio codspeed-criterion-compat@2.7.2
      - name: Build the benchmark targets
        working-directory: core
        run: cargo codspeed build --features tests,services-memory
      - name: Run the benchmarks
        uses: CodSpeedHQ/action@v3
        env:
          OPENDAL_TEST: memory
        with:
          working-directory: core
          run: cargo codspeed run
