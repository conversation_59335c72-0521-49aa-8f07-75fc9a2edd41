# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: Bindings Dotnet CI

on:
  push:
    branches:
      - main
    tags:
      - '*'
  pull_request:
    branches:
      - main
    paths:
      - "bindings/dotnet/**"
      - "core/**"
      - ".github/workflows/ci_bindings_dotnet.yml"
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}-${{ github.event_name }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup dotnet toolchain
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '7.0.x'
      - name: Setup Rust toolchain
        uses: ./.github/actions/setup
      - name: Build & Test
        working-directory: "bindings/dotnet"
        run: |
          cargo build
          dotnet test
