# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: Bindings PHP CI

on:
  # Disable PHP build until https://github.com/apache/opendal/issues/3055 addressed
  #
  #  push:
  #    branches:
  #      - main
  #    tags:
  #      - '*'
  #  pull_request:
  #    branches:
  #      - main
  #    paths:
  #      - "bindings/php/**"
  #      - ".github/workflows/bindings_php.yml"
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}-${{ github.event_name }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  test:
    runs-on: ubuntu-latest

    strategy:
      fail-fast: true
      matrix:
        php: [8.1, 8.2]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          ini-values: error_reporting=E_ALL
          tools: composer:v2
          coverage: none

      - name: Setup Rust toolchain
        uses: ./.github/actions/setup

      - name: Build opendal-php extension
        working-directory: "bindings/php"
        run: cargo build

      - name: Enable opendal-php extension in php.ini
        run: |
          # 1. Find the extension_dir
          extension_dir=$(php -r "echo ini_get('extension_dir');")
          # 2. Create the extension_dir if it doesn't exist
          sudo mkdir -p $extension_dir
          # 3. Copy the extension to the extension_dir
          sudo cp target/debug/libopendal_php.so $extension_dir/libopendal_php.so
          # 4. Enable the extension
          echo "extension=libopendal_php.so" >> $(php -r "echo php_ini_loaded_file();")

      - name: Check PHP Extensions
        run: php -m

      - name: Install Composer dependencies
        working-directory: "bindings/php"
        run: |
          composer update --prefer-stable --prefer-dist --no-interaction --no-progress

      - name: Test PHP
        working-directory: "bindings/php"
        run: |
          composer test
