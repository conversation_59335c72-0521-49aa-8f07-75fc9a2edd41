# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: Integration Fuse3 CI

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main
    paths:
      - "integrations/fuse3/**"
      - "core/**"
      - ".github/workflows/ci_integration_fuse3.yml"

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}-${{ github.event_name }}
  cancel-in-progress: true

jobs:
  check_clippy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup Rust toolchain
        uses: ./.github/actions/setup
        with:
          need-rocksdb: true
          need-protoc: true
          github-token: ${{ secrets.GITHUB_TOKEN }}

      - name: Cargo clippy
        working-directory: integrations/fuse3
        run: cargo clippy --all-targets --all-features -- -D warnings
