# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: Fuzz Test

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main
    paths:
      - "core/src/**"
      - "core/fuzz/**"
      - "!core/src/docs/**"
      - ".github/workflows/fuzz_test.yml"

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}-${{ github.event_name }}
  cancel-in-progress: true

jobs:
  fuzz:
    name: ${{ matrix.cases.service }} / ${{ matrix.cases.setup }} / ${{ matrix.target }}
    runs-on: ubuntu-latest
    strategy:
      matrix:
        target: [ "fuzz_reader", "fuzz_writer" ]
        cases:
          - { service: "memory", setup: "memory" }
          - { service: "fs", setup: "local_fs" }
          - { service: "s3", setup: "0_minio_s3" }
          - { service: "azblob", setup: "azurite_azblob" }
    steps:
      - uses: actions/checkout@v4
      - name: Setup Rust toolchain
        uses: ./.github/actions/setup
      - name: Set Rust Fuzz
        shell: bash
        run: |
          sudo apt-get install -y libfuzzer-14-dev
          rustup install nightly
          cargo +nightly install cargo-fuzz
      - name: Fuzz
        uses: ./.github/actions/fuzz_test
        env:
          CUSTOM_LIBFUZZER_PATH: /usr/lib/llvm-14/lib/libFuzzer.a
        with:
          setup: ${{ matrix.cases.setup }}
          service: ${{ matrix.cases.service }}
          target: ${{ matrix.target }}
