# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: Edge Test

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main
    paths:
      - "core/src/**"
      - "!core/src/docs/**"
      - "!core/src/services/**"
      - "core/src/services/fs/**"
      - "core/src/services/s3/**"
      - ".github/workflows/edge_test.yml"

jobs:
  test_file_write_on_full_disk:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Create disk image
        run: |
          fallocate -l 512K disk.img
          mkfs disk.img

      - name: Mount disk image
        run: |
          mkdir /tmp/test_dir
          sudo mount -o loop disk.img /tmp/test_dir

      - name: Set permissions
        run: sudo chmod a+wr /tmp/test_dir

      - name: Test
        working-directory: core/edge/file_write_on_full_disk
        run: cargo run
        env:
          OPENDAL_FS_ROOT: /tmp/test_dir

  test_s3_read_on_wasm:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Setup Rust toolchain
        uses: ./.github/actions/setup

      - name: Setup for wasm32
        run: |
          rustup target add wasm32-unknown-unknown

      - name: Install Chrome Environment
        run: |
          mkdir -p /tmp/chrome
          wget $(curl https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json | jq -r '.versions | sort_by(.version) | reverse | .[0] | .downloads.chrome | .[] | select(.platform == "linux64") | .url')
          wget $(curl https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json | jq -r '.versions | sort_by(.version) | reverse | .[0] | .downloads.chromedriver | .[] | select(.platform == "linux64") | .url')
          unzip chromedriver-linux64.zip
          unzip chrome-linux64.zip
          cp -r chrome-linux64/ /tmp/chrome/
          cp -r chromedriver-linux64 /tmp/chrome/chromedriver

      - name: Setup wasm-pack
        run: |
          cargo install wasm-pack

      - name: Setup MinIO Server
        shell: bash
        working-directory: fixtures/s3
        run: docker compose -f docker-compose-minio.yml up -d --wait

      - name: Setup test bucket
        shell: bash
        env:
          AWS_ACCESS_KEY_ID: "minioadmin"
          AWS_SECRET_ACCESS_KEY: "minioadmin"
          AWS_EC2_METADATA_DISABLED: "true"
        run: aws --endpoint-url http://127.0.0.1:9000/ s3 mb s3://test

      - name: Test wasm
        working-directory: core/edge/s3_read_on_wasm
        run: |
          export PATH=$PATH:/tmp/chrome/chrome-linux64/:/tmp/chrome/chromedriver-linux64/
          wasm-pack test --chrome --headless

  test_s3_aws_assume_role_with_web_identity:
    runs-on: ubuntu-latest
    if: github.event_name == 'push' || !github.event.pull_request.head.repo.fork
    permissions:
      id-token: write
      contents: read

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Setup Rust toolchain
        uses: ./.github/actions/setup
      - uses: actions/github-script@v7
        id: id-token
        with:
          script: return await core.getIDToken("sts.amazonaws.com")
          result-encoding: string
      - name: Write ID token to file
        working-directory: core/edge/s3_aws_assume_role_with_web_identity
        run: echo "${{ steps.id-token.outputs.result }}" > web_identity_token

      - name: Test
        working-directory: core/edge/s3_aws_assume_role_with_web_identity
        run: cargo run
        env:
          AWS_WEB_IDENTITY_TOKEN_FILE: web_identity_token
          AWS_ROLE_ARN: arn:aws:iam::952853449216:role/opendal-testing-assume
          OPENDAL_TEST: s3
          OPENDAL_S3_ROOT: CI/
          OPENDAL_S3_BUCKET: opendal-testing
          OPENDAL_S3_ROLE_ARN: arn:aws:iam::952853449216:role/opendal-testing
          OPENDAL_S3_REGION: ap-northeast-1
