# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

version: 2
updates:
  - package-ecosystem: "github-actions"
    directory: "/"
    open-pull-requests-limit: 1
    schedule:
      interval: "monthly"

  - package-ecosystem: "cargo"
    directory: "/core"
    open-pull-requests-limit: 1
    schedule:
      interval: "monthly"

  - package-ecosystem: "cargo"
    directory: "/bin/oay"
    open-pull-requests-limit: 1
    schedule:
      interval: "monthly"

  - package-ecosystem: "cargo"
    directory: "/bin/ofs"
    open-pull-requests-limit: 1
    schedule:
      interval: "monthly"

  - package-ecosystem: "cargo"
    directory: "/bin/oli"
    open-pull-requests-limit: 1
    schedule:
      interval: "monthly"

  - package-ecosystem: "cargo"
    directory: "/integrations/dav-server"
    open-pull-requests-limit: 1
    schedule:
      interval: "monthly"

  - package-ecosystem: "cargo"
    directory: "/integrations/object_store"
    open-pull-requests-limit: 1
    schedule:
      interval: "monthly"

  - package-ecosystem: "cargo"
    directory: "/bindings/java"
    open-pull-requests-limit: 1
    schedule:
      interval: "monthly"

  - package-ecosystem: "cargo"
    directory: "/bindings/nodejs"
    open-pull-requests-limit: 1
    schedule:
      interval: "monthly"

  - package-ecosystem: "cargo"
    directory: "/bindings/python"
    open-pull-requests-limit: 1
    schedule:
      interval: "monthly"
